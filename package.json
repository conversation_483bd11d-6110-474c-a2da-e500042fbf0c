{"name": "admin-panel", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "file-saver": "^2.0.5", "firebase": "^10.13.0", "react": "^18.3.1", "react-csv": "^2.2.2", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-router-dom": "^6.26.1", "react-scripts": "^5.0.1", "tailwindcss": "^3.4.10", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11"}}