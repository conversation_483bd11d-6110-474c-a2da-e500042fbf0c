# DS App Tracking Fix Guide

## The Problem
Your DS app is creating tracking sessions and immediately marking them as 'completed', which causes the admin panel to show <PERSON> as offline.

## Current Broken Flow:
```
1. DS Check-in → Create dsTracking document with status: 'active'
2. DS Check-in → Immediately set status: 'completed' ❌
3. Admin panel sees 'completed' status → Shows DS as offline
```

## Correct Flow:
```
1. DS Check-in → Create dsTracking document with status: 'active'
2. DS Working → Keep updating location points, keep status: 'active' ✅
3. DS Check-out → Set status: 'completed' ✅
```

## What You Need to Fix in Your DS App:

### 1. Check-in Function (Start Tracking)
```javascript
// When DS checks in - CREATE tracking session
const startTracking = async (dsId) => {
  const sessionId = `${dsId}_${Date.now()}`;
  
  await firestore.collection('dsTracking').doc(sessionId).set({
    dsId: dsId,
    status: 'active',  // Keep this as 'active'
    startTime: firebase.firestore.FieldValue.serverTimestamp(),
    createdAt: firebase.firestore.FieldValue.serverTimestamp(),
    updatedAt: firebase.firestore.FieldValue.serverTimestamp(),
    totalDistance: 0,
    averageSpeed: 0,
    sessionId: sessionId
    // DO NOT set endTime or status: 'completed' here!
  });
  
  // Start location tracking
  startLocationTracking(sessionId);
};
```

### 2. Location Tracking (During Work)
```javascript
// Continuously update location while DS is working
const updateLocation = async (sessionId, locationData) => {
  // Add location point to subcollection
  await firestore
    .collection('dsTracking')
    .doc(sessionId)
    .collection('locationPoints')
    .add({
      batchTimestamp: firebase.firestore.FieldValue.serverTimestamp(),
      points: [locationData]
    });
  
  // Update main tracking document to show it's still active
  await firestore.collection('dsTracking').doc(sessionId).update({
    updatedAt: firebase.firestore.FieldValue.serverTimestamp(),
    // Keep status as 'active' - don't change it!
    // Update other stats if needed
    totalDistance: calculatedDistance,
    averageSpeed: calculatedSpeed
  });
};
```

### 3. Check-out Function (End Tracking)
```javascript
// When DS checks out - COMPLETE tracking session
const stopTracking = async (sessionId) => {
  await firestore.collection('dsTracking').doc(sessionId).update({
    status: 'completed',  // Only set to completed on check-out
    endTime: firebase.firestore.FieldValue.serverTimestamp(),
    updatedAt: firebase.firestore.FieldValue.serverTimestamp(),
    duration: calculateDuration()
  });
  
  // Stop location tracking
  stopLocationTracking();
};
```

## Key Points:

1. **Never set status to 'completed' during check-in**
2. **Keep status as 'active' throughout the work session**
3. **Only set status to 'completed' when DS checks out**
4. **Update 'updatedAt' field regularly to show the session is alive**
5. **Add location points to the subcollection continuously**

## Quick Fix:

Look for this pattern in your DS app code:
```javascript
// WRONG - Don't do this during check-in
await trackingDoc.update({
  status: 'completed',  // ❌ Remove this line
  endTime: timestamp    // ❌ Remove this line
});
```

Replace with:
```javascript
// CORRECT - Keep session active
await trackingDoc.update({
  updatedAt: firebase.firestore.FieldValue.serverTimestamp(),
  // Don't set status to 'completed' here
});
```

## Testing:

1. **Check-in**: DS should appear as "Tracking" in admin panel
2. **During work**: DS should stay "Tracking" and show location updates
3. **Check-out**: DS should go "Offline" only after explicit check-out

## Database Structure Check:

Your `dsTracking` document should look like this while DS is working:
```javascript
{
  dsId: "QK-DS-000046",
  status: "active",        // Should stay 'active' until check-out
  startTime: timestamp,
  createdAt: timestamp,
  updatedAt: timestamp,    // Should update regularly
  totalDistance: 0,
  averageSpeed: 0,
  sessionId: "QK-DS-000046_1751828887821"
  // endTime should NOT exist until check-out
}
```

Once you fix this in your DS app, the admin panel will work correctly!
