import React, { useState, useEffect } from 'react';
import { auth, firestore } from '../firebaseConfig';
import { collection, query, where, getDocs } from 'firebase/firestore';
import './Navbar.css';

const Navbar = () => {
  const [user, setUser] = useState(null);
  const [adminUser, setAdminUser] = useState(null);
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    // Get current user
    const unsubscribe = auth.onAuthStateChanged(async (currentUser) => {
      setUser(currentUser);

      if (currentUser) {
        // Fetch admin user details from adminusers collection
        try {
          const adminUsersQuery = query(
            collection(firestore, 'adminusers'),
            where('email', '==', currentUser.email)
          );
          const adminUsersSnapshot = await getDocs(adminUsersQuery);

          if (!adminUsersSnapshot.empty) {
            const adminUserData = adminUsersSnapshot.docs[0].data();
            setAdminUser(adminUserData);
            console.log('Admin user data:', adminUserData);
          } else {
            console.log('No admin user found with email:', currentUser.email);
            // Fallback to using email-based name
            setAdminUser({
              name: currentUser.email.split('@')[0],
              email: currentUser.email
            });
          }
        } catch (error) {
          console.error('Error fetching admin user:', error);
          // Fallback to using email-based name
          setAdminUser({
            name: currentUser.email.split('@')[0],
            email: currentUser.email
          });
        }
      } else {
        setAdminUser(null);
      }
    });

    // Update time every minute
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => {
      unsubscribe();
      clearInterval(timer);
    };
  }, []);

  // Format the current time
  const formattedTime = currentTime.toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit'
  });

  const formattedDate = currentTime.toLocaleDateString([], {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <div className="navbar">
      <div className="navbar-center">
        <h1>Admin Dashboard</h1>
      </div>

      <div className="navbar-right">
        <div className="date-time">
          <div className="time">{formattedTime}</div>
          <div className="date">{formattedDate}</div>
        </div>

        <div className="user-profile">
          <div className="avatar">
            {adminUser?.name?.charAt(0).toUpperCase() || user?.email?.charAt(0).toUpperCase() || 'U'}
          </div>
          <div className="user-info">
            <div className="user-name">
              Welcome {adminUser?.name || user?.email?.split('@')[0] || 'User'}
            </div>
            <div className="user-role">Administrator</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Navbar;
