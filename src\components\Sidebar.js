import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { auth } from '../firebaseConfig';
import {
  MdDashboard,
  MdApproval,
  MdAssessment,
  MdAccountTree,
  Md<PERSON>erson,
  MdLogout
} from 'react-icons/md';
import './Sidebar.css';

const Sidebar = () => {
  const location = useLocation();
  const { pathname } = location;

  const handleLogout = () => {
    auth.signOut().then(() => {
      console.log("User signed out");
    }).catch((error) => {
      console.error("Error signing out: ", error);
    });
  };

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <h2 className="text-white text-xl font-bold">Admin Panel</h2>
      </div>

      <nav className="sidebar-nav">
        <ul>
          <li className={pathname === '/' ? 'active' : ''}>
            <Link to="/" className="sidebar-link">
              <MdDashboard className="sidebar-icon" />
              <span>Dashboard</span>
            </Link>
          </li>
          <li className={pathname === '/approvals' ? 'active' : ''}>
            <Link to="/approvals" className="sidebar-link">
              <MdApproval className="sidebar-icon" />
              <span>Approvals</span>
            </Link>
          </li>
          <li className={pathname === '/reports' ? 'active' : ''}>
            <Link to="/reports" className="sidebar-link">
              <MdAssessment className="sidebar-icon" />
              <span>Reports</span>
            </Link>
          </li>
          <li className={pathname === '/network' ? 'active' : ''}>
            <Link to="/network" className="sidebar-link">
              <MdAccountTree className="sidebar-icon" />
              <span>Network</span>
            </Link>
          </li>
          <li className={pathname === '/profile' ? 'active' : ''}>
            <Link to="/profile" className="sidebar-link">
              <MdPerson className="sidebar-icon" />
              <span>Profile</span>
            </Link>
          </li>
        </ul>
      </nav>

      <div className="logout-container">
        <button onClick={handleLogout} className="logout-button">
          <MdLogout className="sidebar-icon" />
          <span>Logout</span>
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
