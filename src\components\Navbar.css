.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: var(--navbar-height);
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 0 1.5rem;
  position: sticky;
  top: 0;
  z-index: 50;
}

.navbar-right {
  display: flex;
  align-items: center;
}

.navbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.navbar-center h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e40af;
}

/* Date and Time */
.date-time {
  margin-right: 1.5rem;
  text-align: right;
}

.time {
  font-size: 1.125rem;
  font-weight: 600;
  color: #334155;
}

.date {
  font-size: 0.75rem;
  color: #64748b;
}



/* User Profile */
.user-profile {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.avatar {
  width: 40px;
  height: 40px;
  background-color: #1e40af;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 0.75rem;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  color: #334155;
  font-size: 0.875rem;
}

.user-role {
  color: #64748b;
  font-size: 0.75rem;
}