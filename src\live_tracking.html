<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DS Live Tracking - Admin Panel</title>
    
    <!-- Google Maps API -->
    <script async defer src="https://maps.googleapis.com/maps/api/js?key=YOUR_GOOGLE_MAPS_API_KEY&callback=initMap"></script>
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            margin: 0;
            font-size: 1.8rem;
        }

        .header .subtitle {
            opacity: 0.9;
            margin-top: 0.5rem;
        }

        .container {
            display: flex;
            height: calc(100vh - 80px);
        }

        .sidebar {
            width: 350px;
            background: white;
            border-right: 1px solid #e0e0e0;
            overflow-y: auto;
        }

        .map-container {
            flex: 1;
            position: relative;
        }

        #map {
            width: 100%;
            height: 100%;
        }

        .controls {
            padding: 1rem;
            border-bottom: 1px solid #e0e0e0;
        }

        .control-group {
            margin-bottom: 1rem;
        }

        .control-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }

        .control-group select, .control-group input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .ds-list {
            padding: 1rem;
        }

        .ds-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .ds-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }

        .ds-item.active {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .ds-item.tracking {
            border-left: 4px solid #4CAF50;
        }

        .ds-item.offline {
            border-left: 4px solid #f44336;
            opacity: 0.7;
        }

        .ds-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.25rem;
        }

        .ds-id {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 0.5rem;
        }

        .ds-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-indicator.online {
            background: #4CAF50;
        }

        .status-indicator.offline {
            background: #f44336;
        }

        .ds-stats {
            font-size: 0.8rem;
            color: #666;
        }

        .ds-stats div {
            margin-bottom: 0.25rem;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .error {
            background: #ffebee;
            color: #c62828;
            padding: 1rem;
            margin: 1rem;
            border-radius: 4px;
            border-left: 4px solid #c62828;
        }

        .info-window {
            max-width: 250px;
        }

        .info-window h3 {
            margin: 0 0 0.5rem 0;
            color: #333;
        }

        .info-window .detail {
            margin-bottom: 0.25rem;
            font-size: 0.9rem;
        }

        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        .refresh-btn:hover {
            background: #5a6fd8;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: 300px;
            }
            
            .map-container {
                height: calc(100vh - 380px);
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>DS Live Tracking</h1>
        <div class="subtitle">Real-time location monitoring for Distribution Salesman</div>
    </div>

    <div class="container">
        <div class="sidebar">
            <div class="controls">
                <div class="control-group">
                    <label for="statusFilter">Filter by Status:</label>
                    <select id="statusFilter">
                        <option value="all">All DS</option>
                        <option value="tracking">Currently Tracking</option>
                        <option value="offline">Offline</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label for="searchDS">Search DS:</label>
                    <input type="text" id="searchDS" placeholder="Search by name or ID...">
                </div>

                <button class="refresh-btn" onclick="refreshData()">Refresh Data</button>
            </div>

            <div class="ds-list" id="dsList">
                <div class="loading">Loading DS data...</div>
            </div>
        </div>

        <div class="map-container">
            <div id="map"></div>
        </div>
    </div>

    <script src="live_tracking.js"></script>
</body>
</html>
