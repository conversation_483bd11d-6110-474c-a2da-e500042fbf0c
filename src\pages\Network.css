/* src/pages/Network.css */

.network-container {
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    text-align: center;
  }

  .network-box-container {
    display: flex;
    justify-content: space-around;
    gap: 20px;
    margin-top: 20px;
    flex-wrap: wrap; /* Allows wrapping on smaller screens */
  }

  .network-box {
    flex: 1; /* This allows the box to take equal space */
    min-width: 300px; /* Ensures a minimum size */
    max-width: 500px; /* Ensures a maximum size */
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    text-align: center;
    margin-bottom: 20px;
  }

  .network-box-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .list-box {
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
    background-color: #ffffff;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-top: 10px;
  }

  .list-header {
    display: flex;
    justify-content: center;
    font-weight: bold;
    padding: 10px;
    background-color: #e0e0e0;
    border-bottom: 1px solid #ccc;
  }

  .list-item {
    display: flex;
    justify-content: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
  }

  .list-item span,
  .list-header span {
    flex: 1;
    text-align: center; /* Center-align text */
    padding: 0 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .error-message {
    color: red;
    font-weight: bold;
    margin-bottom: 20px;
  }

  /* Styles for the export button */
  .export-button-container {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
  }

  .export-button {
    background-color: #007bff;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  .export-button:hover {
    background-color: #0056b3;
  }

  /* Loading indicator */
  .loading-indicator {
    padding: 20px;
    text-align: center;
    color: #666;
  }

  /* Empty list message */
  .empty-list {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
  }

  /* Updated export button styles */
  .export-button {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    padding: 8px 12px;
  }

  /* Status button styles */
  .status-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    padding: 6px 12px;
    border-radius: 20px;
    border: none;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100px;
    margin: 0 auto;
  }

  .status-button.active {
    background-color: #10b981;
    color: white;
  }

  .status-button.inactive {
    background-color: #ef4444;
    color: white;
  }

  .status-button:hover {
    opacity: 0.9;
    transform: translateY(-1px);
  }

  .status-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .status-icon {
    font-size: 1rem;
  }
