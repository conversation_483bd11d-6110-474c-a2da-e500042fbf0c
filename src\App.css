/* General Styles */
:root {
  --sidebar-width: 240px;
  --navbar-height: 70px;
}

.App {
  text-align: center;
}

/* Main Content Container */
.app-container {
  margin-left: var(--sidebar-width);
  padding: 1.5rem;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  transition: margin-left 0.3s ease;
}

/* Ensure Flex Layout for Sidebar and Content */
.flex {
  display: flex;
}

.flex-grow {
  flex: 1;
}

/* Page Container */
.page-container {
  padding: 1.5rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-top: 1rem;
}

/* Card Styles */
.dashboard-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* But<PERSON> Styles */
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  cursor: pointer;
}

.button-primary {
  background-color: #0ea5e9;
  color: white;
  border: none;
}

.button-primary:hover {
  background-color: #0284c7;
}

.button-secondary {
  background-color: #f1f5f9;
  color: #334155;
  border: 1px solid #e2e8f0;
}

.button-secondary:hover {
  background-color: #e2e8f0;
}

.button-danger {
  background-color: #ef4444;
  color: white;
  border: none;
}

.button-danger:hover {
  background-color: #dc2626;
}