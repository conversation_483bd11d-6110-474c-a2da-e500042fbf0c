import React, { useEffect, useState } from 'react';
import { firestore } from '../firebaseConfig'; // Adjust the path if needed
import { collection, getDocs, doc, updateDoc } from 'firebase/firestore'; // Import necessary Firestore methods
import { MdFileDownload, MdCheckCircle, MdCancel } from 'react-icons/md'; // Import icons
import './Network.css'; // Import a separate CSS file for styling

const Network = () => {
  const [wdUsers, setWdUsers] = useState([]);
  const [users, setUsers] = useState([]);
  const [outlets, setOutlets] = useState([]);
  const [loading, setLoading] = useState({
    wdUsers: true,
    users: true,
    outlets: true
  });
  const [updating, setUpdating] = useState({
    id: null,
    collection: null
  });
  const [error, setError] = useState(null);

  // Function to toggle status
  const toggleStatus = async (id, currentStatus, collectionName) => {
    try {
      setUpdating({ id, collection: collectionName });

      // Determine the new status (toggle between 'active' and 'inactive')
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active';

      // Update the document in Firestore
      const docRef = doc(firestore, collectionName, id);
      await updateDoc(docRef, { status: newStatus });

      // Update the local state based on which collection was updated
      if (collectionName === 'wdUsers') {
        setWdUsers(prevUsers =>
          prevUsers.map(user =>
            user.id === id ? { ...user, status: newStatus } : user
          )
        );
      } else if (collectionName === 'users') {
        setUsers(prevUsers =>
          prevUsers.map(user =>
            user.id === id ? { ...user, status: newStatus } : user
          )
        );
      } else if (collectionName === 'outlets') {
        setOutlets(prevOutlets =>
          prevOutlets.map(outlet =>
            outlet.id === id ? { ...outlet, status: newStatus } : outlet
          )
        );
      }

      console.log(`Status updated for ${id} in ${collectionName} to ${newStatus}`);
    } catch (error) {
      console.error(`Error updating status: ${error.message}`);
      setError(`Failed to update status: ${error.message}`);
    } finally {
      setUpdating({ id: null, collection: null });
    }
  };

  useEffect(() => {
    const fetchWdUsers = async () => {
      try {
        setLoading(prev => ({ ...prev, wdUsers: true }));
        // Fetch wdUsers from Firestore
        const wdUsersSnapshot = await getDocs(collection(firestore, 'wdUsers'));
        const wdUsersData = wdUsersSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        }));
        setWdUsers(wdUsersData);
      } catch (err) {
        setError('Failed to fetch wdUsers: ' + err.message);
      } finally {
        setLoading(prev => ({ ...prev, wdUsers: false }));
      }
    };

    const fetchUsers = async () => {
      try {
        setLoading(prev => ({ ...prev, users: true }));
        // Fetch users from Firestore
        const usersSnapshot = await getDocs(collection(firestore, 'users'));
        const usersData = usersSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        }));
        setUsers(usersData);
      } catch (err) {
        setError('Failed to fetch users: ' + err.message);
      } finally {
        setLoading(prev => ({ ...prev, users: false }));
      }
    };

    const fetchOutlets = async () => {
      try {
        setLoading(prev => ({ ...prev, outlets: true }));
        // Fetch outlets from Firestore
        const outletsSnapshot = await getDocs(collection(firestore, 'outlets'));
        const outletsData = outletsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        }));
        setOutlets(outletsData);
      } catch (err) {
        setError('Failed to fetch outlets: ' + err.message);
      } finally {
        setLoading(prev => ({ ...prev, outlets: false }));
      }
    };

    fetchWdUsers();
    fetchUsers();
    fetchOutlets();
  }, []);

  // Function to export Distributors to XLS
  const exportDistributorsToXLS = () => {
    const headers = ['Name', 'ID', 'Status'];
    let csvContent = headers.join(',') + '\n';

    // Add wdUsers data
    wdUsers.forEach(wdUser => {
      const row = [
        wdUser.name || '',
        wdUser.wdId || '',
        wdUser.status || 'inactive'
      ];
      csvContent += row.join(',') + '\n';
    });

    // Create a download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'distributors.xls';
    a.click();
    URL.revokeObjectURL(url);
  };

  // Function to export Salesman to XLS
  const exportSalesmanToXLS = () => {
    const headers = ['Name', 'ID', 'WD Code', 'Status'];
    let csvContent = headers.join(',') + '\n';

    // Add users data
    users.forEach(user => {
      const row = [
        user.name || '',
        user.dsId || '',
        user.wdCode || '',
        user.status || 'inactive'
      ];
      csvContent += row.join(',') + '\n';
    });

    // Create a download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'salesman.xls';
    a.click();
    URL.revokeObjectURL(url);
  };

  // Function to export Outlets to XLS
  const exportOutletsToXLS = () => {
    const headers = ['Name', 'Code', 'Status'];
    let csvContent = headers.join(',') + '\n';

    // Add outlets data
    outlets.forEach(outlet => {
      const row = [
        outlet.outletName || '',
        outlet.outletCode || '',
        outlet.status || 'inactive'
      ];
      csvContent += row.join(',') + '\n';
    });

    // Create a download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'outlets.xls';
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="network-container">
      <h1>Network</h1>
      {error && <p className="error-message">{error}</p>}

      <div className="network-box-container">
        <div className="network-box">
          <div className="network-box-header">
            <h2>Distributors ({wdUsers.length})</h2>
            <button className="export-button" onClick={exportDistributorsToXLS}>
              <MdFileDownload /> Export to XLS
            </button>
          </div>
          <div className="list-box">
            <div className="list-header">
              <span>Name</span>
              <span>ID</span>
              <span>Status</span>
            </div>
            {loading.wdUsers ? (
              <div className="loading-indicator">Loading...</div>
            ) : wdUsers.length > 0 ? (
              wdUsers.map((wdUser) => (
                <div className="list-item" key={wdUser.id}>
                  <span>{wdUser.name || 'N/A'}</span>
                  <span>{wdUser.wdId || 'N/A'}</span>
                  <span>
                    <button
                      className={`status-button ${wdUser.status || 'inactive'}`}
                      onClick={() => toggleStatus(wdUser.id, wdUser.status || 'inactive', 'wdUsers')}
                      disabled={updating.id === wdUser.id}
                    >
                      {updating.id === wdUser.id ?
                        'Updating...' :
                        (wdUser.status === 'active' ?
                          <><MdCheckCircle className="status-icon" /> Active</> :
                          <><MdCancel className="status-icon" /> Inactive</>)
                      }
                    </button>
                  </span>
                </div>
              ))
            ) : (
              <div className="empty-list">No distributors found</div>
            )}
          </div>
        </div>

        <div className="network-box">
          <div className="network-box-header">
            <h2>Salesman ({users.length})</h2>
            <button className="export-button" onClick={exportSalesmanToXLS}>
              <MdFileDownload /> Export to XLS
            </button>
          </div>
          <div className="list-box">
            <div className="list-header">
              <span>Name</span>
              <span>ID</span>
              <span>WD Code</span>
              <span>Status</span>
            </div>
            {loading.users ? (
              <div className="loading-indicator">Loading...</div>
            ) : users.length > 0 ? (
              users.map((user) => (
                <div className="list-item" key={user.id}>
                  <span>{user.name || 'N/A'}</span>
                  <span>{user.dsId || 'N/A'}</span>
                  <span>{user.wdCode || 'N/A'}</span>
                  <span>
                    <button
                      className={`status-button ${user.status || 'inactive'}`}
                      onClick={() => toggleStatus(user.id, user.status || 'inactive', 'users')}
                      disabled={updating.id === user.id}
                    >
                      {updating.id === user.id ?
                        'Updating...' :
                        (user.status === 'active' ?
                          <><MdCheckCircle className="status-icon" /> Active</> :
                          <><MdCancel className="status-icon" /> Inactive</>)
                      }
                    </button>
                  </span>
                </div>
              ))
            ) : (
              <div className="empty-list">No salesman found</div>
            )}
          </div>
        </div>

        <div className="network-box">
          <div className="network-box-header">
            <h2>Outlets ({outlets.length})</h2>
            <button className="export-button" onClick={exportOutletsToXLS}>
              <MdFileDownload /> Export to XLS
            </button>
          </div>
          <div className="list-box">
            <div className="list-header">
              <span>Name</span>
              <span>Code</span>
              <span>Status</span>
            </div>
            {loading.outlets ? (
              <div className="loading-indicator">Loading...</div>
            ) : outlets.length > 0 ? (
              outlets.map((outlet) => (
                <div className="list-item" key={outlet.id}>
                  <span>{outlet.outletName || 'N/A'}</span>
                  <span>{outlet.outletCode || 'N/A'}</span>
                  <span>
                    <button
                      className={`status-button ${outlet.status || 'inactive'}`}
                      onClick={() => toggleStatus(outlet.id, outlet.status || 'inactive', 'outlets')}
                      disabled={updating.id === outlet.id}
                    >
                      {updating.id === outlet.id ?
                        'Updating...' :
                        (outlet.status === 'active' ?
                          <><MdCheckCircle className="status-icon" /> Active</> :
                          <><MdCancel className="status-icon" /> Inactive</>)
                      }
                    </button>
                  </span>
                </div>
              ))
            ) : (
              <div className="empty-list">No outlets found</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Network;
