/* Approvals.css */
.approvals-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.approvals-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.approvals-title {
  font-size: 1.8rem;
  color: #1e40af;
  margin: 0;
}

.tab-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.tab-button {
  padding: 10px 20px;
  background-color: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.tab-button:hover {
  background-color: #e2e8f0;
}

.tab-button.active {
  background-color: #1e40af;
  color: white;
  border-color: #1e40af;
}

.approvals-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.approval-item {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.approval-details {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 0.8rem;
  color: #64748b;
  margin-bottom: 4px;
}

.detail-value {
  font-size: 1rem;
  color: #334155;
  font-weight: 500;
}

.approval-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.approve-button {
  padding: 8px 16px;
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.approve-button:hover {
  background-color: #059669;
}

.reject-button {
  padding: 8px 16px;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.reject-button:hover {
  background-color: #dc2626;
}

.empty-message {
  text-align: center;
  padding: 40px;
  color: #64748b;
  font-style: italic;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
