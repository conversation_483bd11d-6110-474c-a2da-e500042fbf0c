"use strict";
/**
 * Copyright (C) 2016-2019 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.document = exports.XmlProcInst = exports.XmlEntityRef = exports.XmlElement = exports.XmlDtdParamEntityRef = exports.XmlDtdNotation = exports.XmlDtdEntity = exports.XmlDtdElement = exports.XmlDtdAttlist = exports.XmlDtd = exports.XmlDocument = exports.XmlDecl = exports.XmlComment = exports.XmlCharRef = exports.XmlCharData = exports.XmlCdata = exports.XmlAttributeText = exports.XmlAttribute = void 0;
var XmlDocument_1 = __importDefault(require("./nodes/XmlDocument"));
var XmlAttribute_1 = require("./nodes/XmlAttribute");
Object.defineProperty(exports, "XmlAttribute", { enumerable: true, get: function () { return __importDefault(XmlAttribute_1).default; } });
var XmlAttributeText_1 = require("./nodes/XmlAttributeText");
Object.defineProperty(exports, "XmlAttributeText", { enumerable: true, get: function () { return __importDefault(XmlAttributeText_1).default; } });
var XmlCdata_1 = require("./nodes/XmlCdata");
Object.defineProperty(exports, "XmlCdata", { enumerable: true, get: function () { return __importDefault(XmlCdata_1).default; } });
var XmlCharData_1 = require("./nodes/XmlCharData");
Object.defineProperty(exports, "XmlCharData", { enumerable: true, get: function () { return __importDefault(XmlCharData_1).default; } });
var XmlCharRef_1 = require("./nodes/XmlCharRef");
Object.defineProperty(exports, "XmlCharRef", { enumerable: true, get: function () { return __importDefault(XmlCharRef_1).default; } });
var XmlComment_1 = require("./nodes/XmlComment");
Object.defineProperty(exports, "XmlComment", { enumerable: true, get: function () { return __importDefault(XmlComment_1).default; } });
var XmlDecl_1 = require("./nodes/XmlDecl");
Object.defineProperty(exports, "XmlDecl", { enumerable: true, get: function () { return __importDefault(XmlDecl_1).default; } });
var XmlDocument_2 = require("./nodes/XmlDocument");
Object.defineProperty(exports, "XmlDocument", { enumerable: true, get: function () { return __importDefault(XmlDocument_2).default; } });
var XmlDtd_1 = require("./nodes/XmlDtd");
Object.defineProperty(exports, "XmlDtd", { enumerable: true, get: function () { return __importDefault(XmlDtd_1).default; } });
var XmlDtdAttlist_1 = require("./nodes/XmlDtdAttlist");
Object.defineProperty(exports, "XmlDtdAttlist", { enumerable: true, get: function () { return __importDefault(XmlDtdAttlist_1).default; } });
var XmlDtdElement_1 = require("./nodes/XmlDtdElement");
Object.defineProperty(exports, "XmlDtdElement", { enumerable: true, get: function () { return __importDefault(XmlDtdElement_1).default; } });
var XmlDtdEntity_1 = require("./nodes/XmlDtdEntity");
Object.defineProperty(exports, "XmlDtdEntity", { enumerable: true, get: function () { return __importDefault(XmlDtdEntity_1).default; } });
var XmlDtdNotation_1 = require("./nodes/XmlDtdNotation");
Object.defineProperty(exports, "XmlDtdNotation", { enumerable: true, get: function () { return __importDefault(XmlDtdNotation_1).default; } });
var XmlDtdParamEntityRef_1 = require("./nodes/XmlDtdParamEntityRef");
Object.defineProperty(exports, "XmlDtdParamEntityRef", { enumerable: true, get: function () { return __importDefault(XmlDtdParamEntityRef_1).default; } });
var XmlElement_1 = require("./nodes/XmlElement");
Object.defineProperty(exports, "XmlElement", { enumerable: true, get: function () { return __importDefault(XmlElement_1).default; } });
var XmlEntityRef_1 = require("./nodes/XmlEntityRef");
Object.defineProperty(exports, "XmlEntityRef", { enumerable: true, get: function () { return __importDefault(XmlEntityRef_1).default; } });
var XmlProcInst_1 = require("./nodes/XmlProcInst");
Object.defineProperty(exports, "XmlProcInst", { enumerable: true, get: function () { return __importDefault(XmlProcInst_1).default; } });
/**
 * Returns a new XML document with the specified options.
 */
function document(options) {
    if (options === void 0) { options = {}; }
    return new XmlDocument_1.default(options);
}
exports.document = document;
