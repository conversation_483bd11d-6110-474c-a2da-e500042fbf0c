const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
// Try to load service account key, fallback to default credentials
let firebaseConfig;

try {
    const serviceAccount = require('./serviceAccountKey.json');
    firebaseConfig = {
        credential: admin.credential.cert(serviceAccount),
        projectId: 'ds-panel-quickk'
    };
    console.log('Using service account key for Firebase Admin');
} catch (error) {
    console.log('Service account key not found, using application default credentials');
    // Fallback to application default credentials or manual config
    firebaseConfig = {
        projectId: 'ds-panel-quickk'
    };
}

try {
    admin.initializeApp(firebaseConfig);
    console.log('Firebase Admin initialized successfully');
} catch (error) {
    console.error('Failed to initialize Firebase Admin:', error.message);
    console.log('Please ensure you have proper Firebase credentials set up');
    process.exit(1);
}

const db = admin.firestore();

// Express app setup
const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
});

app.use(cors());
app.use(express.json());

// Store active connections and DS data
const activeConnections = new Map();
const dsData = new Map();
const firestoreListeners = new Map();

// Socket.IO connection handling
io.on('connection', (socket) => {
    console.log('Admin client connected:', socket.id);
    activeConnections.set(socket.id, socket);

    // Send current DS data to newly connected client
    socket.emit('ds-data-update', Array.from(dsData.values()));

    // Handle client disconnect
    socket.on('disconnect', () => {
        console.log('Admin client disconnected:', socket.id);
        activeConnections.delete(socket.id);
    });

    // Handle DS selection for focused tracking
    socket.on('select-ds', (dsId) => {
        socket.join(`ds-${dsId}`);
        console.log(`Client ${socket.id} subscribed to DS ${dsId}`);
    });

    // Handle DS unselection
    socket.on('unselect-ds', (dsId) => {
        socket.leave(`ds-${dsId}`);
        console.log(`Client ${socket.id} unsubscribed from DS ${dsId}`);
    });
});

// Initialize DS tracking
async function initializeDSTracking() {
    try {
        console.log('Initializing DS tracking...');
        
        // Load all DS users
        await loadDSUsers();
        
        // Setup listeners for active tracking sessions
        setupTrackingListeners();
        
        console.log('DS tracking initialized successfully');
    } catch (error) {
        console.error('Error initializing DS tracking:', error);
    }
}

// Load DS users from Firestore
async function loadDSUsers() {
    try {
        console.log('Loading DS users from Firestore...');
        const usersSnapshot = await db.collection('users').get();

        if (usersSnapshot.empty) {
            console.log('No users found in Firestore, loading mock data for testing...');
            loadMockData();
            return;
        }

        usersSnapshot.forEach(doc => {
            const userData = doc.data();
            if (userData.dsId) {
                const dsInfo = {
                    id: userData.dsId,
                    name: userData.name || 'Unknown',
                    docId: doc.id,
                    isTracking: false,
                    lastSeen: null,
                    currentLocation: null,
                    sessionStats: null,
                    sessionId: null
                };

                dsData.set(userData.dsId, dsInfo);
            }
        });

        console.log(`Loaded ${dsData.size} DS users from Firestore`);

        // Check for active tracking sessions
        await checkActiveTrackingSessions();

        // Broadcast initial data to all connected clients
        broadcastDSData();

    } catch (error) {
        console.error('Error loading DS users from Firestore:', error);
        console.log('Loading mock data for testing...');
        loadMockData();
    }
}

// Load mock data for testing when Firebase is not available
function loadMockData() {
    const mockUsers = [
        {
            id: 'DS001',
            name: 'John Doe',
            docId: 'mock1',
            isTracking: true,
            lastSeen: new Date(),
            currentLocation: { lat: 28.6139, lng: 77.2090 },
            sessionStats: {
                startTime: new Date(Date.now() - 2 * 60 * 60 * 1000),
                totalDistance: 15.5,
                visitedOutlets: 3,
                status: 'active'
            },
            sessionId: 'session1'
        },
        {
            id: 'DS002',
            name: 'Jane Smith',
            docId: 'mock2',
            isTracking: false,
            lastSeen: new Date(Date.now() - 30 * 60 * 1000),
            currentLocation: null,
            sessionStats: null,
            sessionId: null
        },
        {
            id: 'DS003',
            name: 'Mike Johnson',
            docId: 'mock3',
            isTracking: true,
            lastSeen: new Date(Date.now() - 5 * 60 * 1000),
            currentLocation: { lat: 28.7041, lng: 77.1025 },
            sessionStats: {
                startTime: new Date(Date.now() - 1 * 60 * 60 * 1000),
                totalDistance: 8.2,
                visitedOutlets: 2,
                status: 'active'
            },
            sessionId: 'session2'
        }
    ];

    mockUsers.forEach(user => {
        dsData.set(user.id, user);
    });

    console.log(`Loaded ${dsData.size} mock DS users for testing`);
    broadcastDSData();
}

// Check for active tracking sessions
async function checkActiveTrackingSessions() {
    try {
        const trackingSnapshot = await db.collection('dsTracking')
            .where('status', '==', 'active')
            .get();

        trackingSnapshot.forEach(doc => {
            const trackingData = doc.data();
            const dsId = trackingData.dsId;
            
            if (dsData.has(dsId)) {
                const dsInfo = dsData.get(dsId);
                dsInfo.isTracking = true;
                dsInfo.sessionId = doc.id;
                dsInfo.sessionStats = trackingData;
                dsData.set(dsId, dsInfo);
                
                // Setup location points listener for this DS
                setupLocationPointsListener(dsId, doc.id);
            }
        });
        
        console.log(`Found ${trackingSnapshot.size} active tracking sessions`);
    } catch (error) {
        console.error('Error checking active tracking sessions:', error);
    }
}

// Setup listeners for tracking sessions
function setupTrackingListeners() {
    // Listen for changes in dsTracking collection
    const trackingListener = db.collection('dsTracking')
        .where('status', '==', 'active')
        .onSnapshot(snapshot => {
            snapshot.docChanges().forEach(change => {
                const data = change.doc.data();
                const dsId = data.dsId;
                const sessionId = change.doc.id;

                if (change.type === 'added' || change.type === 'modified') {
                    handleTrackingSessionUpdate(dsId, sessionId, data);
                } else if (change.type === 'removed') {
                    handleTrackingSessionRemoved(dsId);
                }
            });
        });

    firestoreListeners.set('tracking', trackingListener);
}

// Handle tracking session update
function handleTrackingSessionUpdate(dsId, sessionId, sessionData) {
    if (!dsData.has(dsId)) return;

    const dsInfo = dsData.get(dsId);
    dsInfo.isTracking = true;
    dsInfo.sessionId = sessionId;
    dsInfo.sessionStats = sessionData;
    dsInfo.lastSeen = new Date();
    dsData.set(dsId, dsInfo);

    // Setup location points listener if not already exists
    if (!firestoreListeners.has(`location-${dsId}`)) {
        setupLocationPointsListener(dsId, sessionId);
    }

    // Broadcast update to all clients
    broadcastDSUpdate(dsInfo);
    
    console.log(`DS ${dsId} tracking session updated`);
}

// Handle tracking session removed
function handleTrackingSessionRemoved(dsId) {
    if (!dsData.has(dsId)) return;

    const dsInfo = dsData.get(dsId);
    dsInfo.isTracking = false;
    dsInfo.sessionId = null;
    dsInfo.sessionStats = null;
    dsInfo.currentLocation = null;
    dsData.set(dsId, dsInfo);

    // Remove location points listener
    removeLocationPointsListener(dsId);

    // Broadcast update to all clients
    broadcastDSUpdate(dsInfo);
    
    console.log(`DS ${dsId} tracking session ended`);
}

// Setup location points listener for a specific DS
function setupLocationPointsListener(dsId, sessionId) {
    const locationListener = db.collection('dsTracking')
        .doc(sessionId)
        .collection('locationPoints')
        .orderBy('batchTimestamp', 'desc')
        .limit(1)
        .onSnapshot(snapshot => {
            if (!snapshot.empty) {
                const latestBatch = snapshot.docs[0].data();
                if (latestBatch.points && latestBatch.points.length > 0) {
                    const latestPoint = latestBatch.points[latestBatch.points.length - 1];
                    handleLocationUpdate(dsId, {
                        lat: latestPoint.latitude,
                        lng: latestPoint.longitude,
                        timestamp: latestPoint.localTimestamp,
                        accuracy: latestPoint.accuracy,
                        speed: latestPoint.speed,
                        heading: latestPoint.heading
                    });
                }
            }
        });

    firestoreListeners.set(`location-${dsId}`, locationListener);
}

// Remove location points listener
function removeLocationPointsListener(dsId) {
    const listener = firestoreListeners.get(`location-${dsId}`);
    if (listener) {
        listener();
        firestoreListeners.delete(`location-${dsId}`);
    }
}

// Handle location update
function handleLocationUpdate(dsId, locationData) {
    if (!dsData.has(dsId)) return;

    const dsInfo = dsData.get(dsId);
    dsInfo.currentLocation = locationData;
    dsInfo.lastSeen = new Date();
    dsData.set(dsId, dsInfo);

    // Broadcast location update to all clients
    broadcastLocationUpdate(dsId, locationData);
    
    // Send focused update to clients tracking this specific DS
    io.to(`ds-${dsId}`).emit('ds-location-update', {
        dsId,
        location: locationData,
        timestamp: new Date()
    });
}

// Broadcast DS data to all connected clients
function broadcastDSData() {
    const data = Array.from(dsData.values());
    io.emit('ds-data-update', data);
}

// Broadcast single DS update
function broadcastDSUpdate(dsInfo) {
    io.emit('ds-update', dsInfo);
}

// Broadcast location update
function broadcastLocationUpdate(dsId, locationData) {
    io.emit('ds-location-update', {
        dsId,
        location: locationData,
        timestamp: new Date()
    });
}

// REST API endpoints
app.get('/api/ds', (req, res) => {
    const data = Array.from(dsData.values());
    res.json(data);
});

app.get('/api/ds/:dsId', (req, res) => {
    const dsId = req.params.dsId;
    const dsInfo = dsData.get(dsId);
    
    if (!dsInfo) {
        return res.status(404).json({ error: 'DS not found' });
    }
    
    res.json(dsInfo);
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date(),
        connectedClients: activeConnections.size,
        trackedDS: Array.from(dsData.values()).filter(ds => ds.isTracking).length,
        totalDS: dsData.size
    });
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('Shutting down gracefully...');
    
    // Close all Firestore listeners
    firestoreListeners.forEach(listener => listener());
    
    // Close server
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
});

// Start server
const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
    console.log(`Live tracking service running on port ${PORT}`);
    initializeDSTracking();
});

module.exports = { app, server, io };
