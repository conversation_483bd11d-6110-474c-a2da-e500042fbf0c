{"version": 3, "file": "markdownItAnchor.js", "sources": ["../permalink.js", "../index.js"], "sourcesContent": ["let emittedWarning = false\n\nconst position = {\n  false: 'push',\n  true: 'unshift',\n  after: 'push',\n  before: 'unshift'\n}\n\nconst permalinkSymbolMeta = {\n  isPermalinkSymbol: true\n}\n\nexport function legacy (slug, opts, state, idx) {\n  if (!emittedWarning) {\n    const warningText = 'Using deprecated markdown-it-anchor permalink option, see https://github.com/valeriangalliat/markdown-it-anchor#permalinks'\n\n    if (typeof process === 'object' && process && process.emitWarning) {\n      process.emitWarning(warningText)\n    } else {\n      console.warn(warningText)\n    }\n\n    emittedWarning = true\n  }\n\n  const linkTokens = [\n    Object.assign(new state.Token('link_open', 'a', 1), {\n      attrs: [\n        ...(opts.permalinkClass ? [['class', opts.permalinkClass]] : []),\n        ['href', opts.permalinkHref(slug, state)],\n        ...Object.entries(opts.permalinkAttrs(slug, state))\n      ]\n    }),\n    Object.assign(new state.Token('html_block', '', 0), { content: opts.permalinkSymbol, meta: permalinkSymbolMeta }),\n    new state.Token('link_close', 'a', -1)\n  ]\n\n  if (opts.permalinkSpace) {\n    state.tokens[idx + 1].children[position[opts.permalinkBefore]](Object.assign(new state.Token('text', '', 0), { content: ' ' }))\n  }\n\n  state.tokens[idx + 1].children[position[opts.permalinkBefore]](...linkTokens)\n}\n\nexport function renderHref (slug) {\n  return `#${slug}`\n}\n\nexport function renderAttrs (slug) {\n  return {}\n}\n\nconst commonDefaults = {\n  class: 'header-anchor',\n  symbol: '#',\n  renderHref,\n  renderAttrs\n}\n\nexport function makePermalink (renderPermalinkImpl) {\n  function renderPermalink (opts) {\n    opts = Object.assign({}, renderPermalink.defaults, opts)\n\n    return (slug, anchorOpts, state, idx) => {\n      return renderPermalinkImpl(slug, opts, anchorOpts, state, idx)\n    }\n  }\n\n  renderPermalink.defaults = Object.assign({}, commonDefaults)\n  renderPermalink.renderPermalinkImpl = renderPermalinkImpl\n\n  return renderPermalink\n}\n\nexport const linkInsideHeader = makePermalink((slug, opts, anchorOpts, state, idx) => {\n  const linkTokens = [\n    Object.assign(new state.Token('link_open', 'a', 1), {\n      attrs: [\n        ...(opts.class ? [['class', opts.class]] : []),\n        ['href', opts.renderHref(slug, state)],\n        ...(opts.ariaHidden ? [['aria-hidden', 'true']] : []),\n        ...Object.entries(opts.renderAttrs(slug, state))\n      ]\n    }),\n    Object.assign(new state.Token('html_inline', '', 0), { content: opts.symbol, meta: permalinkSymbolMeta }),\n    new state.Token('link_close', 'a', -1)\n  ]\n\n  if (opts.space) {\n    const space = typeof opts.space === 'string' ? opts.space : ' '\n    const type = typeof opts.space === 'string' ? 'html_inline' : 'text'\n    state.tokens[idx + 1].children[position[opts.placement]](Object.assign(new state.Token(type, '', 0), { content: space }))\n  }\n\n  state.tokens[idx + 1].children[position[opts.placement]](...linkTokens)\n})\n\nObject.assign(linkInsideHeader.defaults, {\n  space: true,\n  placement: 'after',\n  ariaHidden: false\n})\n\nexport const ariaHidden = makePermalink(linkInsideHeader.renderPermalinkImpl)\n\nariaHidden.defaults = Object.assign({}, linkInsideHeader.defaults, {\n  ariaHidden: true\n})\n\nexport const headerLink = makePermalink((slug, opts, anchorOpts, state, idx) => {\n  const linkTokens = [\n    Object.assign(new state.Token('link_open', 'a', 1), {\n      attrs: [\n        ...(opts.class ? [['class', opts.class]] : []),\n        ['href', opts.renderHref(slug, state)],\n        ...Object.entries(opts.renderAttrs(slug, state))\n      ]\n    }),\n    ...(opts.safariReaderFix ? [new state.Token('span_open', 'span', 1)] : []),\n    ...state.tokens[idx + 1].children,\n    ...(opts.safariReaderFix ? [new state.Token('span_close', 'span', -1)] : []),\n    new state.Token('link_close', 'a', -1)\n  ]\n\n  state.tokens[idx + 1] = Object.assign(new state.Token('inline', '', 0), {\n    children: linkTokens\n  })\n})\n\nObject.assign(headerLink.defaults, {\n  safariReaderFix: false\n})\n\nexport const linkAfterHeader = makePermalink((slug, opts, anchorOpts, state, idx) => {\n  if (!['visually-hidden', 'aria-label', 'aria-describedby', 'aria-labelledby'].includes(opts.style)) {\n    throw new Error(`\\`permalink.linkAfterHeader\\` called with unknown style option \\`${opts.style}\\``)\n  }\n\n  if (!['aria-describedby', 'aria-labelledby'].includes(opts.style) && !opts.assistiveText) {\n    throw new Error(`\\`permalink.linkAfterHeader\\` called without the \\`assistiveText\\` option in \\`${opts.style}\\` style`)\n  }\n\n  if (opts.style === 'visually-hidden' && !opts.visuallyHiddenClass) {\n    throw new Error('`permalink.linkAfterHeader` called without the `visuallyHiddenClass` option in `visually-hidden` style')\n  }\n\n  const title = state.tokens[idx + 1]\n    .children\n    .filter(token => token.type === 'text' || token.type === 'code_inline')\n    .reduce((acc, t) => acc + t.content, '')\n\n  const subLinkTokens = []\n  const linkAttrs = []\n\n  if (opts.class) {\n    linkAttrs.push(['class', opts.class])\n  }\n\n  linkAttrs.push(['href', opts.renderHref(slug, state)])\n  linkAttrs.push(...Object.entries(opts.renderAttrs(slug, state)))\n\n  if (opts.style === 'visually-hidden') {\n    subLinkTokens.push(\n      Object.assign(new state.Token('span_open', 'span', 1), {\n        attrs: [['class', opts.visuallyHiddenClass]],\n      }),\n      Object.assign(new state.Token('text', '', 0), {\n        content: opts.assistiveText(title)\n      }),\n      new state.Token('span_close', 'span', -1)\n    )\n\n    if (opts.space) {\n      const space = typeof opts.space === 'string' ? opts.space : ' '\n      const type = typeof opts.space === 'string' ? 'html_inline' : 'text'\n      subLinkTokens[position[opts.placement]](Object.assign(new state.Token(type, '', 0), { content: space }))\n    }\n\n    subLinkTokens[position[opts.placement]](\n      Object.assign(new state.Token('span_open', 'span', 1), {\n        attrs: [['aria-hidden', 'true']],\n      }),\n      Object.assign(new state.Token('html_inline', '', 0), {\n        content: opts.symbol,\n        meta: permalinkSymbolMeta\n      }),\n      new state.Token('span_close', 'span', -1)\n    )\n  } else {\n    subLinkTokens.push(\n      Object.assign(new state.Token('html_inline', '', 0), {\n        content: opts.symbol,\n        meta: permalinkSymbolMeta\n      })\n    )\n  }\n\n  if (opts.style === 'aria-label') {\n    linkAttrs.push(['aria-label', opts.assistiveText(title)])\n  } else if (['aria-describedby', 'aria-labelledby'].includes(opts.style)) {\n    linkAttrs.push([opts.style, slug])\n  }\n\n  const linkTokens = [\n    Object.assign(new state.Token('link_open', 'a', 1), {\n      attrs: linkAttrs\n    }),\n    ...subLinkTokens,\n    new state.Token('link_close', 'a', -1),\n  ]\n\n  state.tokens.splice(idx + 3, 0, ...linkTokens)\n\n  if (opts.wrapper) {\n    state.tokens.splice(idx, 0, Object.assign(new state.Token('html_block', '', 0), {\n      content: opts.wrapper[0] + '\\n'\n    }))\n\n    state.tokens.splice(idx + 3 + linkTokens.length + 1, 0, Object.assign(new state.Token('html_block', '', 0), {\n      content: opts.wrapper[1] + '\\n'\n    }))\n  }\n})\n\nObject.assign(linkAfterHeader.defaults, {\n  style: 'visually-hidden',\n  space: true,\n  placement: 'after',\n  wrapper: null\n})\n", "import * as permalink from './permalink'\n\nconst slugify = (s) => encodeURIComponent(String(s).trim().toLowerCase().replace(/\\s+/g, '-'))\n\nfunction getTokensText (tokens) {\n  return tokens\n    .filter(t => ['text', 'code_inline'].includes(t.type))\n    .map(t => t.content)\n    .join('')\n}\n\nfunction uniqueSlug (slug, slugs, failOnNonUnique, startIndex) {\n  let uniq = slug\n  let i = startIndex\n\n  if (failOnNonUnique && Object.prototype.hasOwnProperty.call(slugs, uniq)) {\n    throw new Error(`User defined \\`id\\` attribute \\`${slug}\\` is not unique. Please fix it in your Markdown to continue.`)\n  } else {\n    while (Object.prototype.hasOwnProperty.call(slugs, uniq)) {\n      uniq = `${slug}-${i}`\n      i += 1\n    }\n  }\n\n  slugs[uniq] = true\n\n  return uniq\n}\n\nconst isLevelSelectedNumber = selection => level => level >= selection\nconst isLevelSelectedArray = selection => level => selection.includes(level)\n\nfunction anchor (md, opts) {\n  opts = Object.assign({}, anchor.defaults, opts)\n\n  md.core.ruler.push('anchor', state => {\n    const slugs = {}\n    const tokens = state.tokens\n\n    const isLevelSelected = Array.isArray(opts.level)\n      ? isLevelSelectedArray(opts.level)\n      : isLevelSelectedNumber(opts.level)\n\n    for (let idx = 0; idx < tokens.length; idx++) {\n      const token = tokens[idx]\n\n      if (token.type !== 'heading_open') {\n        continue\n      }\n\n      if (!isLevelSelected(Number(token.tag.substr(1)))) {\n        continue\n      }\n\n      // Aggregate the next token children text.\n      const title = opts.getTokensText(tokens[idx + 1].children)\n\n      let slug = token.attrGet('id')\n\n      if (slug == null) {\n        slug = uniqueSlug(opts.slugify(title), slugs, false, opts.uniqueSlugStartIndex)\n      } else {\n        slug = uniqueSlug(slug, slugs, true, opts.uniqueSlugStartIndex)\n      }\n\n      token.attrSet('id', slug)\n\n      if (opts.tabIndex !== false) {\n        token.attrSet('tabindex', `${opts.tabIndex}`)\n      }\n\n      if (typeof opts.permalink === 'function') {\n        opts.permalink(slug, opts, state, idx)\n      } else if (opts.permalink) {\n        opts.renderPermalink(slug, opts, state, idx)\n      } else if (opts.renderPermalink && opts.renderPermalink !== permalink.legacy) {\n        opts.renderPermalink(slug, opts, state, idx)\n      }\n\n      // A permalink renderer could modify the `tokens` array so\n      // make sure to get the up-to-date index on each iteration.\n      idx = tokens.indexOf(token)\n\n      if (opts.callback) {\n        opts.callback(token, { slug, title })\n      }\n    }\n  })\n}\n\nanchor.permalink = permalink\n\nanchor.defaults = {\n  level: 1,\n  slugify,\n  uniqueSlugStartIndex: 1,\n  tabIndex: '-1',\n  getTokensText,\n\n  // Legacy options.\n  permalink: false,\n  renderPermalink: permalink.legacy,\n  permalinkClass: permalink.ariaHidden.defaults.class,\n  permalinkSpace: permalink.ariaHidden.defaults.space,\n  permalinkSymbol: '¶',\n  permalinkBefore: permalink.ariaHidden.defaults.placement === 'before',\n  permalinkHref: permalink.ariaHidden.defaults.renderHref,\n  permalinkAttrs: permalink.ariaHidden.defaults.renderAttrs\n}\n\n// Dirty hack to make `import anchor from 'markdown-it-anchor'` work with\n// TypeScript which doesn't support the `module` field of `package.json` and\n// will always get the CommonJS version which otherwise wouldn't have a\n// `default` key, resulting in markdown-it-anchor being undefined when being\n// imported that way.\nanchor.default = anchor\n\nexport default anchor\n"], "names": ["emitted<PERSON><PERSON>ning", "position", "false", "true", "after", "before", "permalinkSymbolMeta", "isPermalinkSymbol", "legacy", "slug", "opts", "state", "idx", "_state$tokens$childre", "process", "emitWarning", "warningText", "console", "warn", "linkTokens", "Object", "assign", "Token", "attrs", "permalinkClass", "permalinkHref", "entries", "permalinkAttrs", "content", "permalinkSymbol", "meta", "permalinkSpace", "tokens", "children", "permalinkBefore", "renderAttrs", "commonDefaults", "class", "symbol", "renderHref", "renderPermalinkImpl", "renderPermalink", "defaults", "anchorOpts", "linkInsideHeader", "makePermalink", "_state$tokens$childre2", "concat", "ariaHidden", "space", "placement", "headerLink", "safariReaderFix", "linkAfterHeader", "includes", "style", "Error", "assistiveText", "visuallyHiddenClass", "title", "filter", "token", "type", "reduce", "acc", "t", "linkAttrs", "push", "subLinkTokens", "splice", "wrapper", "length", "uniqueSlug", "slugs", "failOnNonUnique", "startIndex", "uniq", "i", "prototype", "hasOwnProperty", "call", "anchor", "md", "core", "ruler", "selection", "isLevelSelected", "Array", "isArray", "level", "isLevelSelectedNumber", "Number", "tag", "substr", "getTokensText", "attrGet", "slugify", "uniqueSlugStartIndex", "attrSet", "tabIndex", "permalink", "indexOf", "callback", "s", "encodeURIComponent", "String", "trim", "toLowerCase", "replace", "map", "join"], "mappings": "AAAA,IAAIA,GAAiB,EAEPC,EAAG,CACfC,MAAO,OACPC,KAAM,UACNC,MAAO,OACPC,OAAQ,WAGeC,EAAG,CAC1BC,mBAAmB,GAGLC,SAAAA,EAAQC,EAAMC,EAAMC,EAAOC,GACzC,IAAAC,EAAA,IAAKb,EAAgB,CACnB,MAAoB,6HAEG,0BAAYc,SAAWA,QAAQC,YACpDD,QAAQC,YAAYC,GAEpBC,QAAQC,KAAKF,GAGfhB,GAAiB,CACnB,CAEA,IAAgBmB,EAAG,CACjBC,OAAOC,OAAO,IAAIV,EAAMW,MAAM,YAAa,IAAK,GAAI,CAClDC,gBACMb,EAAKc,eAAiB,CAAC,CAAC,QAASd,EAAKc,iBAAmB,IAC7D,CAAC,OAAQd,EAAKe,cAAchB,EAAME,KAC/BS,OAAOM,QAAQhB,EAAKiB,eAAelB,EAAME,OAGhDS,OAAOC,OAAO,IAAIV,EAAMW,MAAM,aAAc,GAAI,GAAI,CAAEM,QAASlB,EAAKmB,gBAAiBC,KAAMxB,IAC3F,IAASK,EAACW,MAAM,aAAc,KAAM,IAGlCZ,EAAKqB,gBACPpB,EAAMqB,OAAOpB,EAAM,GAAGqB,SAAShC,EAASS,EAAKwB,kBAAkBd,OAAOC,OAAO,MAAUC,MAAM,OAAQ,GAAI,GAAI,CAAEM,QAAS,UAG1HjB,EAAMqB,OAAOpB,EAAM,GAAGqB,UAAShC,EAASS,EAAKwB,kBAAqBf,MAAAA,EAAAA,EACpE,CAEO,WAAqBV,GAC1B,MAAA,IAAWA,CACb,CAEgB0B,SAAAA,EAAa1B,GAC3B,MAAO,CAAA,CACT,CAEA,IAAoB2B,EAAG,CACrBC,MAAO,gBACPC,OAAQ,IACRC,WAAAA,EACAJ,YAAAA,GAGK,WAAwBK,GAC7B,SAAwBC,EAAE/B,GAGxB,OAFAA,EAAOU,OAAOC,OAAO,CAAA,EAAIoB,EAAgBC,SAAUhC,YAE3CD,EAAMkC,EAAYhC,EAAOC,GAC/B,OAA0B4B,EAAC/B,EAAMC,EAAMiC,EAAYhC,EAAOC,EAC5D,CACF,CAKA,OAHA6B,EAAgBC,SAAWtB,OAAOC,OAAO,CAAA,EAAIe,GAC7CK,EAAgBD,oBAAsBA,EAGxCC,CAAA,CAEaG,IAAAA,EAAmBC,EAAc,SAACpC,EAAMC,EAAMiC,EAAYhC,EAAOC,GAC5E,IAAAkC,IAAmB,CACjB1B,OAAOC,OAAO,IAAIV,EAAMW,MAAM,YAAa,IAAK,GAAI,CAClDC,MAAK,GAAAwB,OACCrC,EAAU,MAAG,CAAC,CAAC,QAASA,EAAI,QAAW,GAC3C,CAAA,CAAC,OAAQA,EAAK6B,WAAW9B,EAAME,KAC3BD,EAAKsC,WAAa,CAAC,CAAC,cAAe,SAAW,GAC/C5B,OAAOM,QAAQhB,EAAKyB,YAAY1B,EAAME,OAG7CS,OAAOC,OAAO,MAAUC,MAAM,cAAe,GAAI,GAAI,CAAEM,QAASlB,EAAK4B,OAAQR,KAAMxB,IACnF,MAAUgB,MAAM,aAAc,KAAM,IAGtC,GAAIZ,EAAKuC,MAAO,CACd,IAAWA,EAAyB,iBAAXvC,EAACuC,MAAqBvC,EAAKuC,MAAQ,IAE5DtC,EAAMqB,OAAOpB,EAAM,GAAGqB,SAAShC,EAASS,EAAKwC,YAAY9B,OAAOC,OAAO,MAAUC,MAD9C,iBAAXZ,EAACuC,MAAqB,cAAgB,OAC+B,GAAI,GAAI,CAAErB,QAASqB,IAClH,IAEAtC,EAAMqB,OAAOpB,EAAM,GAAGqB,UAAShC,EAASS,EAAKwC,oBAAe/B,EAC9D,GAEAC,OAAOC,OAAOuB,EAAiBF,SAAU,CACvCO,OAAO,EACPC,UAAW,QACXF,YAAY,IAGP,MAAmBH,EAAcD,EAAiBJ,qBAEzDQ,EAAWN,SAAWtB,OAAOC,OAAO,CAAE,EAAEuB,EAAiBF,SAAU,CACjEM,YAAY,QAGSG,EAAGN,EAAc,SAACpC,EAAMC,EAAMiC,EAAYhC,EAAOC,GACtE,IAAMO,GACJC,OAAOC,OAAO,MAAUC,MAAM,YAAa,IAAK,GAAI,CAClDC,gBACMb,EAAI,MAAS,CAAC,CAAC,QAASA,UAAe,GAAE,CAC7C,CAAC,OAAQA,EAAK6B,WAAW9B,EAAME,KAC5BS,OAAOM,QAAQhB,EAAKyB,YAAY1B,EAAME,QAE3CoC,OACErC,EAAK0C,gBAAkB,CAAC,IAASzC,EAACW,MAAM,YAAa,OAAQ,IAAM,GACpEX,EAAMqB,OAAOpB,EAAM,GAAGqB,SACrBvB,EAAK0C,gBAAkB,CAAC,MAAU9B,MAAM,aAAc,QAAS,IAAM,GACzE,CAAA,MAAUA,MAAM,aAAc,KAAM,KAGtCX,EAAMqB,OAAOpB,EAAM,GAAKQ,OAAOC,OAAO,IAAIV,EAAMW,MAAM,SAAU,GAAI,GAAI,CACtEW,SAAUd,GAEd,GAEAC,OAAOC,OAAO8B,EAAWT,SAAU,CACjCU,iBAAiB,QAGSC,EAAGR,EAAc,SAACpC,EAAMC,EAAMiC,EAAYhC,EAAOC,SAC3E,IAAK,CAAC,kBAAmB,aAAc,mBAAoB,mBAAmB0C,SAAS5C,EAAK6C,OAC1F,UAAeC,MAAA,iEAAqE9C,EAAK6C,WAG3F,IAAK,CAAC,mBAAoB,mBAAmBD,SAAS5C,EAAK6C,SAAW7C,EAAK+C,cACzE,MAAM,UAA4F/C,6EAAAA,EAAK6C,MAAK,WAG9G,GAAmB,oBAAf7C,EAAK6C,QAAgC7C,EAAKgD,oBAC5C,MAAM,UAAU,0GAGlB,IAAWC,EAAGhD,EAAMqB,OAAOpB,EAAM,GAC9BqB,SACA2B,OAAO,SAAAC,SAAwB,SAAVA,EAACC,MAAkC,gBAAfD,EAAMC,IAAsB,GACrEC,OAAO,SAACC,EAAKC,GAAC,OAAQD,EAAGC,EAAErC,OAAO,EAAE,MAEjB,GAChBsC,EAAY,GASlB,GAPIxD,EAAI,OACNwD,EAAUC,KAAK,CAAC,QAASzD,UAG3BwD,EAAUC,KAAK,CAAC,OAAQzD,EAAK6B,WAAW9B,EAAME,KAC9CuD,EAAUC,WAAVD,EAAkB9C,OAAOM,QAAQhB,EAAKyB,YAAY1B,EAAME,KAErC,oBAAfD,EAAK6C,MAA6B,CAWpC,GAVAa,EAAcD,KACZ/C,OAAOC,OAAO,MAAUC,MAAM,YAAa,OAAQ,GAAI,CACrDC,MAAO,CAAC,CAAC,QAASb,EAAKgD,wBAEzBtC,OAAOC,OAAO,MAAUC,MAAM,OAAQ,GAAI,GAAI,CAC5CM,QAASlB,EAAK+C,cAAcE,KAE9B,IAAIhD,EAAMW,MAAM,aAAc,QAAS,IAGrCZ,EAAKuC,MAAO,CACd,IAAMA,EAA8B,iBAAfvC,EAAKuC,MAAqBvC,EAAKuC,MAAQ,IAE5DmB,EAAcnE,EAASS,EAAKwC,YAAY9B,OAAOC,OAAO,IAAIV,EAAMW,MAD7B,iBAAfZ,EAAKuC,MAAqB,cAAgB,OACc,GAAI,GAAI,CAAErB,QAASqB,IACjG,CAEAmB,EAAcnE,EAASS,EAAKwC,YAC1B9B,OAAOC,OAAO,IAAIV,EAAMW,MAAM,YAAa,OAAQ,GAAI,CACrDC,MAAO,CAAC,CAAC,cAAe,WAE1BH,OAAOC,OAAO,IAAIV,EAAMW,MAAM,cAAe,GAAI,GAAI,CACnDM,QAASlB,EAAK4B,OACdR,KAAMxB,IAER,IAAIK,EAAMW,MAAM,aAAc,QAAS,GAE3C,MACE8C,EAAcD,KACZ/C,OAAOC,OAAO,IAASV,EAACW,MAAM,cAAe,GAAI,GAAI,CACnDM,QAASlB,EAAK4B,OACdR,KAAMxB,KAKO,eAAfI,EAAK6C,MACPW,EAAUC,KAAK,CAAC,aAAczD,EAAK+C,cAAcE,KACxC,CAAC,mBAAoB,mBAAmBL,SAAS5C,EAAK6C,QAC/DW,EAAUC,KAAK,CAACzD,EAAK6C,MAAO9C,IAG9B,IAAgBU,EAAA,CACdC,OAAOC,OAAO,IAASV,EAACW,MAAM,YAAa,IAAK,GAAI,CAClDC,MAAO2C,YAENE,EAAa,CAChB,IAAIzD,EAAMW,MAAM,aAAc,KAAM,MAGtCX,EAAAA,EAAMqB,QAAOqC,OAAOzD,MAAAA,EAAAA,CAAAA,EAAM,EAAG,GAACmC,OAAK5B,IAE/BT,EAAK4D,UACP3D,EAAMqB,OAAOqC,OAAOzD,EAAK,EAAGQ,OAAOC,OAAO,IAASV,EAACW,MAAM,aAAc,GAAI,GAAI,CAC9EM,QAASlB,EAAK4D,QAAQ,GAAK,QAG7B3D,EAAMqB,OAAOqC,OAAOzD,EAAM,EAAIO,EAAWoD,OAAS,EAAG,EAAGnD,OAAOC,OAAO,IAAIV,EAAMW,MAAM,aAAc,GAAI,GAAI,CAC1GM,QAASlB,EAAK4D,QAAQ,GAAK,QAGjC,GCpNA,SAASE,EAAY/D,EAAMgE,EAAOC,EAAiBC,GACjD,IAAQC,EAAGnE,EACNoE,EAAGF,EAER,GAAID,GAAmBtD,OAAO0D,UAAUC,eAAeC,KAAKP,EAAOG,GACjE,MAAUpB,IAAAA,sCAAyC/C,EAAI,gEAEvD,KAAOW,OAAO0D,UAAUC,eAAeC,KAAKP,EAAOG,IACjDA,EAAUnE,EAAI,IAAIoE,EAClBA,GAAK,EAMT,OAFAJ,EAAMG,IAAQ,EAGhBA,CAAA,CAKA,SAASK,EAAQC,EAAIxE,GACnBA,EAAOU,OAAOC,OAAO,CAAE,EAAE4D,EAAOvC,SAAUhC,GAE1CwE,EAAGC,KAAKC,MAAMjB,KAAK,SAAU,SAAAxD,GAQ3B,IAPA,IANyB0E,IAMX,CAAA,EACFrD,EAAGrB,EAAMqB,OAEAsD,EAAGC,MAAMC,QAAQ9E,EAAK+E,QATlBJ,EAUA3E,EAAK+E,MAVQA,SAAAA,GAASJ,OAAAA,EAAU/B,SAASmC,EAAM,GAD9C,SAAAJ,UAAaI,SAAAA,GAASA,OAAAA,GAASJ,CAAS,CAAA,CAY9DK,CAAsBhF,EAAK+E,SAEhB,EAAG7E,EAAMoB,EAAOuC,OAAQ3D,IAAO,CAC5C,MAAcoB,EAAOpB,GAErB,GAAmB,iBAAfiD,EAAMC,MAILwB,EAAgBK,OAAO9B,EAAM+B,IAAIC,OAAO,KAA7C,CAKA,IAAMlC,EAAQjD,EAAKoF,cAAc9D,EAAOpB,EAAM,GAAGqB,UAEzCxB,EAAGoD,EAAMkC,QAAQ,MAGvBtF,EADU,MAARA,EACK+D,EAAW9D,EAAKsF,QAAQrC,GAAQc,GAAO,EAAO/D,EAAKuF,sBAEnDzB,EAAW/D,EAAMgE,GAAO,EAAM/D,EAAKuF,sBAG5CpC,EAAMqC,QAAQ,KAAMzF,IAEE,IAAlBC,EAAKyF,UACPtC,EAAMqC,QAAQ,cAAexF,EAAKyF,UAGN,mBAAfzF,EAAC0F,UACd1F,EAAK0F,UAAU3F,EAAMC,EAAMC,EAAOC,IACzBF,EAAK0F,WAEL1F,EAAK+B,iBAAmB/B,EAAK+B,kBAAoB2D,IAD1D1F,EAAK+B,gBAAgBhC,EAAMC,EAAMC,EAAOC,GAO1CA,EAAMoB,EAAOqE,QAAQxC,GAEjBnD,EAAK4F,UACP5F,EAAK4F,SAASzC,EAAO,CAAEpD,KAAAA,EAAMkD,MAAAA,GAhC/B,CAkCF,CACF,EACF,CDyIAvC,OAAOC,OAAOgC,EAAgBX,SAAU,CACtCa,MAAO,kBACPN,OAAO,EACPC,UAAW,QACXoB,QAAS,OC3IXW,EAAOmB,8IAEPnB,EAAOvC,SAAW,CAChB+C,MAAO,EACPO,QA5Fc,SAACO,GAAMC,OAAAA,mBAAmBC,OAAOF,GAAGG,OAAOC,cAAcC,QAAQ,OAAQ,KAAK,EA6F5FX,qBAAsB,EACtBE,SAAU,KACVL,cA7FF,SAAwB9D,GACtB,OAAOA,EACJ4B,OAAO,SAAAK,GAAK,MAAA,CAAC,OAAQ,eAAeX,SAASW,EAAEH,KAAK,GACpD+C,IAAI,SAAA5C,GAAC,OAAKA,EAACrC,OAAO,GAClBkF,KAAK,GACV,EA2FEV,WAAW,EACX3D,gBAAiB2D,EACjB5E,eAAgB4E,EAAqB1D,SAAc,MACnDX,eAAgBqE,EAAqB1D,SAASO,MAC9CpB,gBAAiB,IACjBK,gBAA6D,WAA5CkE,EAAqB1D,SAASQ,UAC/CzB,cAAe2E,EAAqB1D,SAASH,WAC7CZ,eAAgByE,EAAqB1D,SAASP,aAQhD8C,EAAc,QAAGA"}