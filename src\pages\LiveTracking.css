.live-tracking-container {
  padding: 0;
  min-height: calc(100vh - var(--navbar-height) - 3rem);
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  overflow-y: auto;
}

.live-tracking-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 2rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.live-tracking-header h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.live-tracking-header .subtitle {
  opacity: 0.9;
  margin: 0.5rem 0 0 0;
  font-size: 0.95rem;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-dot.connected {
  background: #4CAF50;
}

.status-dot.disconnected {
  background: #ff9800;
}

.status-dot.error {
  background: #f44336;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.tracking-content {
  display: flex;
  flex: 1;
  min-height: 600px;
}

.tracking-sidebar {
  width: 350px;
  background: white;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  min-height: 600px;
}

.controls {
  padding: 1rem;
  border-bottom: 1px solid #e0e0e0;
  background: #fafafa;
  flex-shrink: 0;
}

.control-group {
  margin-bottom: 1rem;
}

.control-group:last-child {
  margin-bottom: 0;
}

.control-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.control-group select, 
.control-group input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.control-group select:focus,
.control-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.button-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.25rem;
}

.refresh-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.4rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
  text-align: center;
}

.refresh-btn:hover {
  background: #5a6fd8;
}

.refresh-btn.secondary {
  background: #28a745;
}

.refresh-btn.secondary:hover {
  background: #218838;
}

.refresh-btn.test {
  background: #ffc107;
  color: #212529;
}

.refresh-btn.test:hover {
  background: #e0a800;
}

.ds-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  min-height: 0;
}

.ds-item {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.ds-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transform: translateY(-1px);
}

.ds-item.active {
  border-color: #667eea;
  background: #f8f9ff;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.ds-item.tracking {
  border-left: 4px solid #4CAF50;
}

.ds-item.offline {
  border-left: 4px solid #f44336;
  opacity: 0.8;
}

.ds-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
  font-size: 1rem;
}

.ds-id {
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.ds-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
  font-weight: 500;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-indicator.online {
  background: #4CAF50;
  box-shadow: 0 0 4px rgba(76, 175, 80, 0.4);
}

.status-indicator.offline {
  background: #f44336;
  box-shadow: 0 0 4px rgba(244, 67, 54, 0.4);
}

.ds-stats {
  font-size: 0.8rem;
  color: #666;
}

.ds-stats div {
  margin-bottom: 0.25rem;
}

.ds-stats div:last-child {
  margin-bottom: 0;
}

.map-container {
  flex: 1;
  position: relative;
  background: #f0f0f0;
}

.google-map {
  width: 100%;
  height: 100%;
}

.loading, .error, .no-data {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-style: italic;
}

.error {
  background: #ffebee;
  color: #c62828;
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 4px;
  border-left: 4px solid #c62828;
  font-style: normal;
}

/* Info Window Styles (Global styles for Google Maps InfoWindow) */
.info-window {
  max-width: 250px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.info-window h3 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
}

.info-window .detail {
  margin-bottom: 0.25rem;
  font-size: 0.85rem;
  color: #555;
}

.info-window .detail:last-child {
  margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .tracking-sidebar {
    width: 300px;
  }
}

@media (max-width: 768px) {
  .tracking-content {
    flex-direction: column;
  }

  .tracking-sidebar {
    width: 100%;
    height: 300px;
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
  }

  .map-container {
    height: calc(100vh - var(--navbar-height) - 380px);
    min-height: 300px;
  }

  .live-tracking-header {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .live-tracking-header h1 {
    font-size: 1.5rem;
  }

  .connection-status {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .tracking-sidebar {
    height: 250px;
  }

  .controls {
    padding: 0.75rem;
  }

  .ds-list {
    padding: 0.75rem;
  }

  .ds-item {
    padding: 0.75rem;
  }

  .live-tracking-header {
    padding: 0.75rem;
  }

  .header-content {
    gap: 0.75rem;
  }

  .connection-status {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
}
