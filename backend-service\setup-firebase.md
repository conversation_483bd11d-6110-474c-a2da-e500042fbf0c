# Firebase Service Account Setup

## Quick Setup Steps

1. **Open Firebase Console**
   - Go to: https://console.firebase.google.com/
   - Select your project: `ds-panel-quickk`

2. **Navigate to Service Accounts**
   - Click the gear icon (⚙️) in the top left
   - Select "Project settings"
   - Click on the "Service accounts" tab

3. **Generate Private Key**
   - Scroll down to "Firebase Admin SDK"
   - Click "Generate new private key"
   - Click "Generate key" in the popup

4. **Save the File**
   - A JSON file will be downloaded
   - Rename it to: `serviceAccountKey.json`
   - Move it to the `backend-service` folder (same folder as this file)

5. **Restart the Service**
   ```bash
   npm run dev
   ```

## Security Note
- Never commit the `serviceAccountKey.json` file to version control
- Keep this file secure as it provides admin access to your Firebase project

## Alternative: Using Environment Variables

If you prefer not to use a file, you can set up environment variables instead. Let me know if you'd like help with that approach.

## Troubleshooting

If you're still having issues:
1. Make sure the file is named exactly `serviceAccountKey.json`
2. Make sure it's in the `backend-service` directory
3. Check that the JSON file is valid (not corrupted during download)
4. Verify your Firebase project ID is correct in the server.js file
