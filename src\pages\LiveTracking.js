import React, { useState, useEffect, useRef } from 'react';
import { firestore } from '../firebaseConfig';
import { collection, query, where, getDocs, onSnapshot, orderBy, limit } from 'firebase/firestore';
import io from 'socket.io-client';
import './LiveTracking.css';

const LiveTracking = () => {
  const [dsData, setDsData] = useState([]);
  const [filteredDsData, setFilteredDsData] = useState([]);
  const [selectedDs, setSelectedDs] = useState(null);
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const mapRef = useRef(null);
  const googleMapRef = useRef(null);
  const markersRef = useRef(new Map());
  const infoWindowRef = useRef(null);
  const socketRef = useRef(null);

  // Initialize Google Maps
  useEffect(() => {
    const initMap = () => {
      if (window.google && mapRef.current) {
        const map = new window.google.maps.Map(mapRef.current, {
          center: { lat: 28.6139, lng: 77.2090 }, // Default to Delhi
          zoom: 10,
          styles: [
            {
              featureType: 'poi',
              elementType: 'labels',
              stylers: [{ visibility: 'off' }]
            }
          ]
        });

        googleMapRef.current = map;
        infoWindowRef.current = new window.google.maps.InfoWindow();

        console.log('✅ Google Maps initialized successfully');
        console.log('Map center:', map.getCenter().toJSON());
        console.log('Map zoom:', map.getZoom());

        // Load DS data after map is initialized
        loadDSData();
      }
    };

    // Check if Google Maps is already loaded
    if (window.google) {
      initMap();
    } else {
      // Load Google Maps API
      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.REACT_APP_GOOGLE_MAPS_API_KEY}&callback=initMap`;
      script.async = true;
      script.defer = true;
      window.initMap = initMap;
      document.head.appendChild(script);
    }

    return () => {
      // Cleanup
      if (window.initMap) {
        delete window.initMap;
      }
      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null;
      }
    };
  }, []);

  // Load DS data from Firebase
  const loadDSData = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('Loading DS data from Firebase...');

      // Load DS users from Firebase
      const usersQuery = query(collection(firestore, 'users'));
      const usersSnapshot = await getDocs(usersQuery);

      const dsUsers = [];
      usersSnapshot.forEach(doc => {
        const userData = doc.data();
        console.log('User data:', userData);
        if (userData.dsId) {
          dsUsers.push({
            id: userData.dsId,
            name: userData.name || 'Unknown DS',
            docId: doc.id,
            isTracking: false,
            lastSeen: null,
            currentLocation: null,
            sessionStats: null,
            sessionId: null
          });
        }
      });

      console.log('Found DS users:', dsUsers);

      // Check for active tracking sessions
      const trackingQuery = query(
        collection(firestore, 'dsTracking'),
        where('status', '==', 'active')
      );
      const trackingSnapshot = await getDocs(trackingQuery);

      console.log('Active tracking sessions:', trackingSnapshot.size);

      // Map tracking sessions to DS users
      const trackingMap = new Map();
      trackingSnapshot.forEach(doc => {
        const trackingData = doc.data();
        console.log('Tracking data:', doc.id, trackingData);
        trackingMap.set(trackingData.dsId, {
          sessionId: doc.id,
          sessionData: trackingData
        });
      });

      // Update DS users with tracking information and get their latest location
      const updatedDsUsers = await Promise.all(dsUsers.map(async ds => {
        if (trackingMap.has(ds.id)) {
          const tracking = trackingMap.get(ds.id);
          const updatedDs = {
            ...ds,
            isTracking: true,
            sessionId: tracking.sessionId,
            sessionStats: tracking.sessionData,
            lastSeen: tracking.sessionData.lastUpdate ? tracking.sessionData.lastUpdate.toDate() : new Date()
          };

          // Try to get the latest location from the tracking session
          try {
            console.log('Fetching location for DS:', ds.id, 'Session:', tracking.sessionId);

            const locationQuery = query(
              collection(firestore, 'dsTracking', tracking.sessionId, 'locationPoints'),
              orderBy('batchTimestamp', 'desc'),
              limit(1)
            );
            const locationSnapshot = await getDocs(locationQuery);

            console.log('Location query result for', ds.id, '- Empty:', locationSnapshot.empty, 'Size:', locationSnapshot.size);

            if (!locationSnapshot.empty) {
              const latestBatch = locationSnapshot.docs[0].data();
              console.log('Latest location batch for', ds.id, ':', latestBatch);

              if (latestBatch.points && latestBatch.points.length > 0) {
                const latestPoint = latestBatch.points[latestBatch.points.length - 1];
                console.log('Latest point for', ds.id, ':', latestPoint);

                // Validate coordinates
                if (latestPoint.latitude && latestPoint.longitude) {
                  updatedDs.currentLocation = {
                    lat: parseFloat(latestPoint.latitude),
                    lng: parseFloat(latestPoint.longitude),
                    timestamp: latestPoint.localTimestamp,
                    accuracy: latestPoint.accuracy,
                    speed: latestPoint.speed,
                    heading: latestPoint.heading
                  };
                  console.log('Set location for', ds.id, ':', updatedDs.currentLocation);
                } else {
                  console.warn('Invalid coordinates for DS', ds.id, ':', latestPoint);
                }
              } else {
                console.log('No points in latest batch for DS', ds.id);
              }
            } else {
              console.log('No location data found for DS', ds.id);

              // Let's also check if there are any location documents at all
              const allLocationQuery = collection(firestore, 'dsTracking', tracking.sessionId, 'locationPoints');
              const allLocationSnapshot = await getDocs(allLocationQuery);
              console.log('Total location documents for', ds.id, ':', allLocationSnapshot.size);

              if (!allLocationSnapshot.empty) {
                console.log('Available location documents:');
                allLocationSnapshot.forEach(doc => {
                  console.log('Doc ID:', doc.id, 'Data:', doc.data());
                });
              }
            }
          } catch (locationError) {
            console.error('Error fetching location for DS', ds.id, ':', locationError);
          }

          return updatedDs;
        }
        return ds;
      }));

      console.log('Final DS data:', updatedDsUsers);

      setDsData(updatedDsUsers);
      setFilteredDsData(updatedDsUsers);
      updateMapMarkers(updatedDsUsers);

      // Setup real-time listeners
      setupRealtimeListeners(updatedDsUsers);

      // Setup location listeners for active tracking sessions
      setupLocationListeners(updatedDsUsers);

      setLoading(false);
    } catch (err) {
      console.error('Error loading DS data:', err);
      setError('Failed to load DS data. Please try again.');
      setLoading(false);
    }
  };

  // Setup real-time listeners
  const setupRealtimeListeners = (dsUsers) => {
    console.log('Setting up real-time listeners...');

    // For now, let's skip Socket.IO and focus on Firebase listeners
    setConnectionStatus('connected');

    // Setup Firebase listeners for tracking sessions
    const trackingQuery = query(
      collection(firestore, 'dsTracking'),
      where('status', '==', 'active')
    );

    const unsubscribeTracking = onSnapshot(trackingQuery, async (snapshot) => {
      console.log('Tracking snapshot received, changes:', snapshot.docChanges().length);

      for (const change of snapshot.docChanges()) {
        const data = change.doc.data();
        const dsId = data.dsId;
        const sessionId = change.doc.id;

        console.log('Processing change:', change.type, 'for DS:', dsId);

        if (change.type === 'added' || change.type === 'modified') {
          // Get the latest location for this DS
          let currentLocation = null;
          try {
            const locationQuery = query(
              collection(firestore, 'dsTracking', sessionId, 'locationPoints'),
              orderBy('batchTimestamp', 'desc'),
              limit(1)
            );
            const locationSnapshot = await getDocs(locationQuery);

            if (!locationSnapshot.empty) {
              const latestBatch = locationSnapshot.docs[0].data();
              if (latestBatch.points && latestBatch.points.length > 0) {
                const latestPoint = latestBatch.points[latestBatch.points.length - 1];
                currentLocation = {
                  lat: latestPoint.latitude,
                  lng: latestPoint.longitude,
                  timestamp: latestPoint.localTimestamp,
                  accuracy: latestPoint.accuracy,
                  speed: latestPoint.speed,
                  heading: latestPoint.heading
                };
                console.log('Found current location for DS', dsId, ':', currentLocation);
              }
            }
          } catch (locationError) {
            console.error('Error fetching location for DS', dsId, ':', locationError);
          }

          setDsData(prevData => {
            const updatedData = prevData.map(ds => {
              if (ds.id === dsId) {
                const updatedDs = {
                  ...ds,
                  isTracking: data.status === 'active',
                  sessionId,
                  sessionStats: data,
                  lastSeen: new Date(),
                  currentLocation: currentLocation || ds.currentLocation
                };

                console.log('Updated DS data:', updatedDs);
                return updatedDs;
              }
              return ds;
            });
            updateMapMarkers(updatedData);
            return updatedData;
          });

        } else if (change.type === 'removed') {
          console.log('Tracking session removed for DS:', dsId);
          setDsData(prevData => {
            const updatedData = prevData.map(ds =>
              ds.id === dsId
                ? {
                    ...ds,
                    isTracking: false,
                    sessionId: null,
                    sessionStats: null,
                    currentLocation: null
                  }
                : ds
            );
            updateMapMarkers(updatedData);
            return updatedData;
          });
        }
      }
    }, (error) => {
      console.error('Error in tracking listener:', error);
      setConnectionStatus('error');
    });

    return () => {
      console.log('Cleaning up listeners...');
      unsubscribeTracking();
    };
  };

  // Setup location listeners for active DS
  const setupLocationListeners = (dsUsers) => {
    console.log('Setting up location listeners for active DS...');

    dsUsers.forEach(ds => {
      if (ds.isTracking && ds.sessionId) {
        console.log('Setting up location listener for DS:', ds.id, 'Session:', ds.sessionId);

        // Listen to location points for this DS
        const locationQuery = query(
          collection(firestore, 'dsTracking', ds.sessionId, 'locationPoints'),
          orderBy('batchTimestamp', 'desc'),
          limit(1)
        );

        const unsubscribeLocation = onSnapshot(locationQuery, (snapshot) => {
          console.log('Location update received for DS:', ds.id);

          if (!snapshot.empty) {
            const latestBatch = snapshot.docs[0].data();
            console.log('New location batch for', ds.id, ':', latestBatch);

            if (latestBatch.points && latestBatch.points.length > 0) {
              const latestPoint = latestBatch.points[latestBatch.points.length - 1];
              console.log('New location point for', ds.id, ':', latestPoint);

              if (latestPoint.latitude && latestPoint.longitude) {
                const newLocation = {
                  lat: parseFloat(latestPoint.latitude),
                  lng: parseFloat(latestPoint.longitude),
                  timestamp: latestPoint.localTimestamp,
                  accuracy: latestPoint.accuracy,
                  speed: latestPoint.speed,
                  heading: latestPoint.heading
                };

                console.log('Updating DS location in real-time:', ds.id, newLocation);

                // Update DS data with new location
                setDsData(prevData => {
                  const updatedData = prevData.map(dsItem => {
                    if (dsItem.id === ds.id) {
                      const updatedDs = {
                        ...dsItem,
                        currentLocation: newLocation,
                        lastSeen: new Date(),
                        isTracking: true
                      };
                      console.log('Updated DS with new location:', updatedDs);
                      return updatedDs;
                    }
                    return dsItem;
                  });
                  updateMapMarkers(updatedData);
                  return updatedData;
                });
              }
            }
          }
        }, (error) => {
          console.error('Error in location listener for DS', ds.id, ':', error);
        });

        // Store the unsubscribe function (you might want to manage these properly)
        // For now, we'll let them run
      }
    });
  };



  // Update map markers
  const updateMapMarkers = (data) => {
    if (!googleMapRef.current) {
      console.log('Google map not ready yet');
      return;
    }

    console.log('Updating map markers with data:', data);

    // Clear existing markers
    markersRef.current.forEach(marker => marker.setMap(null));
    markersRef.current.clear();

    let markersCreated = 0;

    // Add new markers
    data.forEach(ds => {
      console.log('Processing DS for marker:', ds.id, 'Location:', ds.currentLocation, 'Tracking:', ds.isTracking);

      if (ds.currentLocation && ds.currentLocation.lat && ds.currentLocation.lng) {
        const lat = parseFloat(ds.currentLocation.lat);
        const lng = parseFloat(ds.currentLocation.lng);

        // Validate coordinates
        if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
          console.error('Invalid coordinates for DS', ds.id, ':', { lat, lng });
          return;
        }

        const position = { lat, lng };
        console.log('Creating marker at position:', position, 'for DS:', ds.id);

        try {
          // Use a simpler marker icon first to test
          const marker = new window.google.maps.Marker({
            position: position,
            map: googleMapRef.current,
            title: `${ds.name} (${ds.id})`,
            label: {
              text: ds.isTracking ? '●' : '○',
              color: ds.isTracking ? '#4CAF50' : '#f44336',
              fontSize: '16px',
              fontWeight: 'bold'
            },
            icon: {
              path: window.google.maps.SymbolPath.CIRCLE,
              scale: 12,
              fillColor: ds.isTracking ? '#4CAF50' : '#f44336',
              fillOpacity: 1,
              strokeColor: 'white',
              strokeWeight: 2
            }
          });

          marker.addListener('click', () => {
            console.log('Marker clicked for DS:', ds.id);
            showInfoWindow(marker, ds);
          });

          markersRef.current.set(ds.id, marker);
          markersCreated++;
          console.log('✅ Marker successfully created for DS:', ds.id, 'at position:', position);

          // If this is the first marker, center the map on it
          if (markersCreated === 1) {
            console.log('Centering map on first marker:', position);
            googleMapRef.current.setCenter(position);
            googleMapRef.current.setZoom(12);
          }

        } catch (markerError) {
          console.error('Error creating marker for DS', ds.id, ':', markerError);
        }
      } else {
        console.log('❌ No valid location for DS:', ds.id, 'Location data:', ds.currentLocation);
      }
    });

    console.log(`✅ Total markers created: ${markersCreated} out of ${data.length} DS`);

    // If we have markers, adjust map bounds to show all of them
    if (markersCreated > 1) {
      const bounds = new window.google.maps.LatLngBounds();
      markersRef.current.forEach(marker => {
        bounds.extend(marker.getPosition());
      });
      googleMapRef.current.fitBounds(bounds);
      console.log('Adjusted map bounds to show all markers');
    }
  };

  // Show info window
  const showInfoWindow = (marker, ds) => {
    console.log('Showing info window for DS:', ds);

    const lastSeenText = ds.lastSeen ?
      (typeof ds.lastSeen === 'string' ? ds.lastSeen : ds.lastSeen.toLocaleString()) :
      'Never';

    const content = `
      <div class="info-window">
        <h3>${ds.name}</h3>
        <div class="detail"><strong>ID:</strong> ${ds.id}</div>
        <div class="detail"><strong>Status:</strong> ${ds.isTracking ? 'Tracking' : 'Offline'}</div>
        <div class="detail"><strong>Last Seen:</strong> ${lastSeenText}</div>
        ${ds.currentLocation ? `
          <div class="detail"><strong>Location:</strong> ${ds.currentLocation.lat.toFixed(6)}, ${ds.currentLocation.lng.toFixed(6)}</div>
          ${ds.currentLocation.accuracy ? `<div class="detail"><strong>Accuracy:</strong> ${ds.currentLocation.accuracy}m</div>` : ''}
        ` : ''}
        ${ds.sessionStats ? `
          <div class="detail"><strong>Distance:</strong> ${ds.sessionStats.totalDistance || 0} km</div>
          <div class="detail"><strong>Outlets:</strong> ${ds.sessionStats.visitedOutlets || 0}</div>
        ` : ''}
      </div>
    `;

    infoWindowRef.current.setContent(content);
    infoWindowRef.current.open(googleMapRef.current, marker);
  };

  // Filter DS data
  useEffect(() => {
    let filtered = dsData;

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(ds => {
        if (statusFilter === 'tracking') return ds.isTracking;
        if (statusFilter === 'offline') return !ds.isTracking;
        return true;
      });
    }

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(ds =>
        ds.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ds.id.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredDsData(filtered);
  }, [dsData, statusFilter, searchQuery]);

  // Handle DS selection
  const handleDsSelect = (ds) => {
    console.log('DS selected:', ds);
    setSelectedDs(ds);

    if (ds.currentLocation && ds.currentLocation.lat && ds.currentLocation.lng && googleMapRef.current) {
      const position = {
        lat: parseFloat(ds.currentLocation.lat),
        lng: parseFloat(ds.currentLocation.lng)
      };

      console.log('Centering map on DS location:', position);
      googleMapRef.current.setCenter(position);
      googleMapRef.current.setZoom(16);

      const marker = markersRef.current.get(ds.id);
      if (marker) {
        showInfoWindow(marker, ds);
      }
    } else {
      console.log('No valid location for selected DS:', ds.currentLocation);

      // If no location, try to fetch it manually
      if (ds.sessionId) {
        fetchLatestLocation(ds.id, ds.sessionId);
      }
    }
  };

  // Manually fetch latest location for a DS
  const fetchLatestLocation = async (dsId, sessionId) => {
    try {
      console.log('Manually fetching location for DS:', dsId);

      const locationQuery = query(
        collection(firestore, 'dsTracking', sessionId, 'locationPoints'),
        orderBy('batchTimestamp', 'desc'),
        limit(1)
      );
      const locationSnapshot = await getDocs(locationQuery);

      if (!locationSnapshot.empty) {
        const latestBatch = locationSnapshot.docs[0].data();
        console.log('Manually fetched location batch:', latestBatch);

        if (latestBatch.points && latestBatch.points.length > 0) {
          const latestPoint = latestBatch.points[latestBatch.points.length - 1];

          if (latestPoint.latitude && latestPoint.longitude) {
            const newLocation = {
              lat: parseFloat(latestPoint.latitude),
              lng: parseFloat(latestPoint.longitude),
              timestamp: latestPoint.localTimestamp,
              accuracy: latestPoint.accuracy,
              speed: latestPoint.speed,
              heading: latestPoint.heading
            };

            console.log('Manually fetched location:', newLocation);

            // Update DS data
            setDsData(prevData => {
              const updatedData = prevData.map(ds => {
                if (ds.id === dsId) {
                  return { ...ds, currentLocation: newLocation, lastSeen: new Date() };
                }
                return ds;
              });
              updateMapMarkers(updatedData);
              return updatedData;
            });
          }
        }
      }
    } catch (error) {
      console.error('Error manually fetching location:', error);
    }
  };

  // Refresh data
  const refreshData = () => {
    console.log('Refreshing all data...');
    loadDSData();
  };

  // Refresh locations for all active DS
  const refreshLocations = async () => {
    console.log('Refreshing locations for all active DS...');

    const activeDs = dsData.filter(ds => ds.isTracking && ds.sessionId);
    console.log('Active DS to refresh:', activeDs.length);

    for (const ds of activeDs) {
      await fetchLatestLocation(ds.id, ds.sessionId);
    }
  };

  // Test marker creation
  const testMarkers = () => {
    console.log('🧪 Testing marker creation...');
    console.log('Google Maps available:', !!window.google);
    console.log('Map reference:', !!googleMapRef.current);
    console.log('Current DS data count:', dsData.length);
    console.log('Current DS data:', dsData);

    // Detailed analysis of DS data
    dsData.forEach((ds, index) => {
      console.log(`DS ${index + 1}:`, {
        id: ds.id,
        name: ds.name,
        isTracking: ds.isTracking,
        hasLocation: !!ds.currentLocation,
        location: ds.currentLocation,
        locationValid: ds.currentLocation && ds.currentLocation.lat && ds.currentLocation.lng
      });
    });

    if (googleMapRef.current) {
      console.log('Map center:', googleMapRef.current.getCenter().toJSON());
      console.log('Map zoom:', googleMapRef.current.getZoom());

      // Test with a known location (Delhi)
      const testMarker = new window.google.maps.Marker({
        position: { lat: 28.7041, lng: 77.1025 },
        map: googleMapRef.current,
        title: 'Test Marker',
        icon: {
          path: window.google.maps.SymbolPath.CIRCLE,
          scale: 15,
          fillColor: '#FF0000',
          fillOpacity: 1,
          strokeColor: 'white',
          strokeWeight: 2
        }
      });

      console.log('✅ Test marker created at:', { lat: 28.7041, lng: 77.1025 });

      // Also test with the actual DS coordinates if available
      const trackingDs = dsData.find(ds => ds.isTracking && ds.currentLocation);
      if (trackingDs) {
        console.log('Creating test marker with actual DS coordinates:', trackingDs.currentLocation);
        const dsTestMarker = new window.google.maps.Marker({
          position: {
            lat: parseFloat(trackingDs.currentLocation.lat),
            lng: parseFloat(trackingDs.currentLocation.lng)
          },
          map: googleMapRef.current,
          title: `Test DS: ${trackingDs.name}`,
          icon: {
            path: window.google.maps.SymbolPath.CIRCLE,
            scale: 20,
            fillColor: '#00FF00',
            fillOpacity: 1,
            strokeColor: 'white',
            strokeWeight: 2
          }
        });

        setTimeout(() => {
          dsTestMarker.setMap(null);
          console.log('DS test marker removed');
        }, 5000);
      }

      // Remove test marker after 3 seconds
      setTimeout(() => {
        testMarker.setMap(null);
        console.log('Test marker removed');
      }, 3000);
    }

    // Force update markers with current data
    console.log('🔄 Forcing marker update...');
    updateMapMarkers(dsData);
  };

  // Test with exact coordinates from your logs
  const testExactCoordinates = () => {
    console.log('🎯 Testing with exact coordinates from logs...');

    if (googleMapRef.current) {
      // Your exact coordinates: lat: 28.7041699, lng: 77.1951638
      const exactPosition = { lat: 28.7041699, lng: 77.1951638 };

      console.log('Creating marker with exact coordinates:', exactPosition);

      const exactMarker = new window.google.maps.Marker({
        position: exactPosition,
        map: googleMapRef.current,
        title: 'Exact DS Location (QK-DS-000046)',
        icon: {
          path: window.google.maps.SymbolPath.CIRCLE,
          scale: 15,
          fillColor: '#4CAF50',
          fillOpacity: 1,
          strokeColor: 'white',
          strokeWeight: 3
        }
      });

      // Center map on this location
      googleMapRef.current.setCenter(exactPosition);
      googleMapRef.current.setZoom(15);

      console.log('✅ Exact coordinate marker created and map centered');

      // Remove after 10 seconds
      setTimeout(() => {
        exactMarker.setMap(null);
        console.log('Exact coordinate marker removed');
      }, 10000);
    }
  };

  const formatTime = (date) => {
    if (!date) return 'Never';
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  };

  return (
    <div className="live-tracking-container">
      <div className="live-tracking-header">
        <div className="header-content">
          <div>
            <h1>DS Live Tracking</h1>
            <p className="subtitle">Real-time location monitoring for Distribution Salesman</p>
          </div>
          <div className="connection-status">
            <div className={`status-dot ${connectionStatus}`}></div>
            <span className="status-text">
              {connectionStatus === 'connected' ? 'Live' :
               connectionStatus === 'error' ? 'Error' : 'Connecting...'}
            </span>
          </div>
        </div>
      </div>

      <div className="tracking-content">
        <div className="tracking-sidebar">
          <div className="controls">
            <div className="control-group">
              <label htmlFor="statusFilter">Filter by Status:</label>
              <select 
                id="statusFilter" 
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="all">All DS</option>
                <option value="tracking">Currently Tracking</option>
                <option value="offline">Offline</option>
              </select>
            </div>
            
            <div className="control-group">
              <label htmlFor="searchDS">Search DS:</label>
              <input 
                type="text" 
                id="searchDS" 
                placeholder="Search by name or ID..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div className="button-group">
              <button className="refresh-btn" onClick={refreshData}>
                Refresh Data
              </button>
              <button className="refresh-btn secondary" onClick={refreshLocations}>
                Refresh Locations
              </button>
              <button className="refresh-btn test" onClick={testMarkers}>
                Test Markers
              </button>
              <button className="refresh-btn test" onClick={testExactCoordinates}>
                Test Exact Location
              </button>
            </div>
          </div>

          <div className="ds-list">
            {loading ? (
              <div className="loading">Loading DS data...</div>
            ) : error ? (
              <div className="error">{error}</div>
            ) : filteredDsData.length === 0 ? (
              <div className="no-data">No DS found matching your criteria.</div>
            ) : (
              filteredDsData.map(ds => (
                <div 
                  key={ds.id}
                  className={`ds-item ${selectedDs?.id === ds.id ? 'active' : ''} ${ds.isTracking ? 'tracking' : 'offline'}`}
                  onClick={() => handleDsSelect(ds)}
                >
                  <div className="ds-name">{ds.name}</div>
                  <div className="ds-id">ID: {ds.id}</div>
                  <div className="ds-status">
                    <div className={`status-indicator ${ds.isTracking ? 'online' : 'offline'}`}></div>
                    <span>{ds.isTracking ? 'Tracking' : 'Offline'}</span>
                  </div>
                  <div className="ds-stats">
                    <div>Last seen: {formatTime(ds.lastSeen)}</div>
                    {ds.sessionStats && (
                      <>
                        <div>Distance: {ds.sessionStats.totalDistance} km</div>
                        <div>Outlets: {ds.sessionStats.visitedOutlets}</div>
                      </>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        <div className="map-container">
          <div ref={mapRef} className="google-map"></div>
        </div>
      </div>
    </div>
  );
};

export default LiveTracking;
