/* src/index.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-neutral-50 text-neutral-800;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1 {
    @apply text-2xl font-bold text-neutral-900;
  }

  h2 {
    @apply text-xl font-semibold text-neutral-800;
  }

  h3 {
    @apply text-lg font-medium text-neutral-800;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-all duration-200;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700;
  }

  .btn-secondary {
    @apply bg-secondary-600 text-white hover:bg-secondary-700;
  }

  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700;
  }

  .card {
    @apply bg-white rounded-card shadow-card p-6 transition-shadow duration-300 hover:shadow-card-hover;
  }
}
