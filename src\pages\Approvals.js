import { useState, useEffect } from 'react';
import { firestore } from '../firebaseConfig'; // Adjust the path if necessary
import { collection, query, where, onSnapshot, doc, updateDoc } from 'firebase/firestore';
import './Approvals.css';

const Approvals = () => {
  const [approvals, setApprovals] = useState([]);
  const [currentCollection, setCurrentCollection] = useState('users'); // Default to 'users'
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(true);

    const fetchApprovals = (collectionName) => {
      const collectionRef = collection(firestore, collectionName);
      const q = query(collectionRef, where('kyc', '==', 'pending'));

      const unsubscribe = onSnapshot(q, (snapshot) => {
        const approvalsList = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        console.log(`Fetched Approvals from ${collectionName}:`, approvalsList); // Log fetched data
        setApprovals(approvalsList);
        setLoading(false);
      });

      return unsubscribe;
    };

    console.log(`Listening to changes in ${currentCollection} collection`); // Log current collection being listened to
    const unsubscribe = fetchApprovals(currentCollection);

    return () => {
      console.log(`Unsubscribed from ${currentCollection} collection`); // Log unsubscribe action
      unsubscribe();
    };
  }, [currentCollection]);

  const handleApproval = async (id, status) => {
    try {
      const collectionName = currentCollection === 'users' ? 'users' : 'wdUsers';
      const userDoc = doc(firestore, collectionName, id);
      console.log(`Updating ${collectionName} document with ID ${id} to status ${status}`); // Log before updating
      await updateDoc(userDoc, { kyc: status });
      console.log(`Document with ID ${id} updated to status ${status}`); // Log after update
    } catch (error) {
      console.error("Error updating approval status:", error);
      alert(`Error: ${error.message}`);
    }
  };

  return (
    <div className="approvals-container">
      <div className="approvals-header">
        <h1 className="approvals-title">KYC Approvals</h1>
      </div>

      <div className="tab-buttons">
        <button
          className={`tab-button ${currentCollection === 'users' ? 'active' : ''}`}
          onClick={() => setCurrentCollection('users')}
        >
          Salesman Users
        </button>
        <button
          className={`tab-button ${currentCollection === 'wdUsers' ? 'active' : ''}`}
          onClick={() => setCurrentCollection('wdUsers')}
        >
          Distributor Users
        </button>
      </div>

      {loading ? (
        <div className="empty-message">Loading approvals...</div>
      ) : approvals.length > 0 ? (
        <ul className="approvals-list">
          {approvals.map(item => (
            <li key={item.id} className="approval-item">
              <div className="approval-details">
                <div className="detail-item">
                  <span className="detail-label">Name</span>
                  <span className="detail-value">{item.name || 'N/A'}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Mobile Number</span>
                  <span className="detail-value">{item.mobileNumber || 'N/A'}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">State</span>
                  <span className="detail-value">{item.state || 'N/A'}</span>
                </div>
              </div>
              <div className="approval-actions">
                <button
                  className="approve-button"
                  onClick={() => handleApproval(item.id, 'approved')}
                >
                  Approve
                </button>
                <button
                  className="reject-button"
                  onClick={() => handleApproval(item.id, 'rejected')}
                >
                  Reject
                </button>
              </div>
            </li>
          ))}
        </ul>
      ) : (
        <div className="empty-message">No pending approvals found</div>
      )}
    </div>
  );
};

export default Approvals;
